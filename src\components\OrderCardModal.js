// OrderCardModal.js - Fancy centered card modal for order details with mobile-friendly buttons

import React, { useState, useEffect, useMemo } from 'react';
import { collection, addDoc, query, where, orderBy, onSnapshot, serverTimestamp } from 'firebase/firestore';

// Order status mapping
const ORDER_STATUS_MAPPING = {
  'open': { label: 'Open Order', class: 'open', color: '#3B82F6' },
  'open-order': { label: 'Open Order', class: 'open', color: '#3B82F6' },
  'pending': { label: 'Pending', class: 'pending', color: '#F59E0B' },
  'secure': { label: 'Secured', class: 'secure', color: '#10B981' },
  'secured': { label: 'Secured', class: 'secure', color: '#10B981' },
  'pending-pickup': { label: 'Pending Pickup', class: 'pending', color: '#F59E0B' },
  'awaiting-pickup': { label: 'Awaiting Pickup', class: 'pending', color: '#F59E0B' },
  'claim': { label: 'Claim', class: 'claim', color: '#EC4899' },
  'restricted': { label: 'Restricted', class: 'restricted', color: '#EF4444' },
  'not-secure': { label: 'Not Secure', class: 'open', color: '#3B82F6' },
  'unsecure': { label: 'Unsecure', class: 'open', color: '#3B82F6' },
  '': { label: 'No Status', class: 'open', color: '#6B7280' },
  undefined: { label: 'No Status', class: 'open', color: '#6B7280' },
  null: { label: 'No Status', class: 'open', color: '#6B7280' }
};

// Driver type detection keywords
const DRIVER_TYPE_KEYWORDS = {
  tow: ['tow', 'truck', 'wrecker', 'flatbed', 'rollback', 'recovery', 'towing'],
  camera: ['camera', 'spot', 'car', 'surveillance', 'scout', 'spotter']
};

// Format address helper
const formatAddress = (address) => {
  if (typeof address === 'string') return address;
  if (!address) return 'No address';
  
  const parts = [];
  if (address.street) parts.push(address.street);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.zip) parts.push(address.zip);
  
  return parts.join(', ') || 'No address';
};

// Format date helper
const formatDate = (timestamp) => {
  if (!timestamp) return 'N/A';
  
  let date;
  if (timestamp.toDate) {
    date = timestamp.toDate();
  } else if (timestamp instanceof Date) {
    date = timestamp;
  } else {
    date = new Date(timestamp);
  }
  
  return date.toLocaleString();
};

// Get vehicle image URL helper
const getVehicleImageUrl = (order) => {
  if (order.vehicleImage) return order.vehicleImage;
  if (order.images && order.images.length > 0) return order.images[0].url || order.images[0];
  return null;
};

const OrderCardModal = ({ 
  order, 
  onClose, 
  onNavigate, 
  onSecure, 
  currentUser,
  userProfile,
  db,
  team,
  isMobile = false 
}) => {
  const [orderCheckIns, setOrderCheckIns] = useState([]);
  const [loadingCheckIns, setLoadingCheckIns] = useState(true);

  if (!order) return null;

  // Determine if current user is a camera car driver
  const isCameraCarDriver = useMemo(() => {
    if (!userProfile || !currentUser) return false;
    
    // Check driver type
    if (userProfile.driverType === 'camera') return true;
    
    // Check tags for camera car indicators
    if (userProfile.tags && Array.isArray(userProfile.tags)) {
      const hasCarTag = userProfile.tags.some(tag => 
        DRIVER_TYPE_KEYWORDS.camera.some(keyword => 
          (tag.name || '').toLowerCase().includes(keyword)
        )
      );
      if (hasCarTag) return true;
    }
    
    // Check job title
    const jobTitle = (userProfile.jobTitle || '').toLowerCase();
    if (DRIVER_TYPE_KEYWORDS.camera.some(keyword => jobTitle.includes(keyword))) {
      return true;
    }
    
    // Default assume camera car if not explicitly tow truck
    const isTowTruck = userProfile.driverType === 'tow' ||
      (userProfile.tags && Array.isArray(userProfile.tags) && 
       userProfile.tags.some(tag => 
         DRIVER_TYPE_KEYWORDS.tow.some(keyword => 
           (tag.name || '').toLowerCase().includes(keyword)
         )
       ));
    
    return !isTowTruck;
  }, [userProfile, currentUser]);

  // Determine if current user can secure vehicles
  const canSecureVehicles = useMemo(() => {
    if (!userProfile || !currentUser) return false;
    
    // Check if user is tow truck driver
    if (userProfile.driverType === 'tow') return true;
    
    // Check tags for tow truck indicators
    if (userProfile.tags && Array.isArray(userProfile.tags)) {
      const hasTowTag = userProfile.tags.some(tag => 
        DRIVER_TYPE_KEYWORDS.tow.some(keyword => 
          (tag.name || '').toLowerCase().includes(keyword)
        )
      );
      if (hasTowTag) return true;
    }
    
    // Check job title
    const jobTitle = (userProfile.jobTitle || '').toLowerCase();
    if (DRIVER_TYPE_KEYWORDS.tow.some(keyword => jobTitle.includes(keyword))) {
      return true;
    }
    
    return false;
  }, [userProfile, currentUser]);

  // Load check-in history
  useEffect(() => {
    if (!db || !order.id) return;

    const loadCheckIns = async () => {
      try {
        setLoadingCheckIns(true);
        const checkInsQuery = query(
          collection(db, 'orderCheckIns'),
          where('orderId', '==', order.id),
          orderBy('timestamp', 'desc')
        );
        
        const unsubscribe = onSnapshot(checkInsQuery, (snapshot) => {
          const checkIns = [];
          snapshot.docs.forEach(doc => {
            checkIns.push({
              id: doc.id,
              ...doc.data()
            });
          });
          setOrderCheckIns(checkIns);
          setLoadingCheckIns(false);
        });

        return unsubscribe;
      } catch (error) {
        console.error('Error loading check-ins:', error);
        setLoadingCheckIns(false);
      }
    };

    loadCheckIns();
  }, [db, order.id]);

  const statusInfo = ORDER_STATUS_MAPPING[order.status] || ORDER_STATUS_MAPPING['open'];

  // Handle navigation
  const handleNavigate = () => {
    if (onNavigate) {
      onNavigate(order);
    }
  };

  // Handle secure
  const handleSecure = () => {
    if (onSecure) {
      onSecure(order);
    }
  };

  // Handle camera car check-in
  const handleCameraCheckIn = async (actionType) => {
    try {
      const checkInData = {
        orderId: order.id,
        actionType: actionType,
        notes: `${actionType === 'located' ? 'Vehicle located' : actionType === 'not_present' ? 'Vehicle not currently present' : 'Vehicle blocked in'}`,
        timestamp: serverTimestamp(),
        userId: currentUser.id,
        userName: currentUser.displayName || currentUser.email,
        teamId: team.id
      };

      await addDoc(collection(db, 'orderCheckIns'), checkInData);
      
      // Show success message
      alert(`✅ ${actionType === 'located' ? 'Vehicle Located' : actionType === 'not_present' ? 'Not Present' : 'Blocked In'} check-in recorded!`);
    } catch (error) {
      console.error('Error recording check-in:', error);
      alert('Failed to record check-in. Please try again.');
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4"
      style={{ WebkitTapHighlightColor: 'transparent' }}
    >
      <div
        className={`bg-gray-900 rounded-2xl shadow-2xl border border-gray-700 max-w-2xl w-full max-h-[90vh] overflow-y-auto ${
          isMobile ? 'mx-2' : 'mx-4'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-t-2xl p-6 border-b border-gray-700">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-white mb-2 flex items-center gap-3">
                🚗 {order.year} {order.make} {order.model}
              </h2>
              <div 
                className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold text-white"
                style={{ backgroundColor: statusInfo.color }}
              >
                {statusInfo.label}
              </div>
            </div>
            <button
              onClick={onClose}
              className={`${isMobile ? 'w-12 h-12 text-xl min-w-[48px] min-h-[48px]' : 'w-10 h-10 text-lg'} bg-red-600 hover:bg-red-700 active:bg-red-800 text-white rounded-full flex items-center justify-center transition-colors shadow-lg touch-manipulation`}
              style={{ WebkitTapHighlightColor: 'transparent' }}
            >
              ✕
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Vehicle Image */}
          {getVehicleImageUrl(order) && (
            <div className="mb-6">
              <img 
                src={getVehicleImageUrl(order)} 
                alt={`${order.make} ${order.model}`}
                className="w-full h-48 object-cover rounded-lg shadow-md"
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />
            </div>
          )}

          {/* Vehicle Information Grid */}
          <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-2'} gap-4 mb-6`}>
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="text-gray-400 text-sm font-medium">Make</div>
              <div className="text-white text-lg font-semibold">{order.make || 'N/A'}</div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="text-gray-400 text-sm font-medium">Model</div>
              <div className="text-white text-lg font-semibold">{order.model || 'N/A'}</div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="text-gray-400 text-sm font-medium">Year</div>
              <div className="text-white text-lg font-semibold">{order.year || 'N/A'}</div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="text-gray-400 text-sm font-medium">Color</div>
              <div className="text-white text-lg font-semibold">{order.color || 'N/A'}</div>
            </div>
            
            <div className={`bg-gray-800 rounded-lg p-4 ${isMobile ? '' : 'col-span-2'}`}>
              <div className="text-gray-400 text-sm font-medium">VIN</div>
              <div className="text-white text-lg font-mono font-semibold break-all">{order.vin || 'N/A'}</div>
            </div>
            
            {order.licensePlate && (
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="text-gray-400 text-sm font-medium">License Plate</div>
                <div className="text-white text-lg font-mono font-semibold">{order.licensePlate}</div>
              </div>
            )}
            
            {order.caseNumber && (
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="text-gray-400 text-sm font-medium">Case Number</div>
                <div className="text-white text-lg font-semibold">{order.caseNumber}</div>
              </div>
            )}
          </div>

          {/* Addresses Section */}
          {order.addresses && order.addresses.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                📍 {order.addresses.length > 1 ? `Addresses (${order.addresses.length})` : 'Address'}
              </h3>
              <div className="space-y-3">
                {order.addresses.map((address, index) => (
                  <div key={index} className="bg-gray-800 rounded-lg p-4 border-l-4 border-blue-500">
                    <div className="flex items-start gap-3">
                      {order.addresses.length > 1 && (
                        <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">
                          {index + 1}
                        </div>
                      )}
                      <div className="flex-1">
                        <div className="text-white font-medium">{formatAddress(address)}</div>
                        {address.position && address.position.lat && address.position.lng && (
                          <div className="text-gray-400 text-sm mt-1 font-mono">
                            GPS: {address.position.lat.toFixed(6)}, {address.position.lng.toFixed(6)}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Check-in History */}
          {orderCheckIns.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                📋 Check-in History ({orderCheckIns.length})
              </h3>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {orderCheckIns.map(checkIn => (
                  <div key={checkIn.id} className="bg-gray-800 rounded-lg p-3">
                    <div className="flex justify-between items-start mb-1">
                      <div className="text-white font-medium">
                        {checkIn.actionType === 'located' && '✅ Vehicle Located'}
                        {checkIn.actionType === 'not_present' && '⏰ Not Present'}
                        {checkIn.actionType === 'blocked' && '🚧 Blocked In'}
                      </div>
                      <div className="text-gray-400 text-sm">
                        {formatDate(checkIn.timestamp)}
                      </div>
                    </div>
                    <div className="text-gray-400 text-sm">
                      By: {checkIn.userName}
                    </div>
                    {checkIn.notes && (
                      <div className="text-gray-300 text-sm mt-1">
                        {checkIn.notes}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Notes */}
          {(order.details || order.notes) && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                📝 Notes
              </h3>
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="text-gray-300">{order.details || order.notes}</div>
              </div>
            </div>
          )}

          {/* Timestamps */}
          <div className="mb-6 text-sm text-gray-400">
            <div>Created: {formatDate(order.createdAt)}</div>
            {order.updatedAt && (
              <div>Updated: {formatDate(order.updatedAt)}</div>
            )}
            {order.securedBy && (
              <div>Secured by: {order.securedBy}</div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-gray-800 rounded-b-2xl p-6 border-t border-gray-700">
          <div className={`grid ${isMobile ? 'grid-cols-1 gap-3' : 'grid-cols-2 gap-4'}`}>
            {/* Navigation Button */}
            <button
              onClick={handleNavigate}
              className={`${isMobile ? 'py-4 min-h-[56px]' : 'py-3'} px-6 bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white rounded-xl font-semibold text-lg flex items-center justify-center gap-3 transition-colors shadow-lg touch-manipulation`}
              style={{ WebkitTapHighlightColor: 'transparent' }}
            >
              🗺️ Navigate
            </button>

            {/* Secure Button (for tow truck drivers) */}
            {canSecureVehicles && (
              <button
                onClick={handleSecure}
                className={`${isMobile ? 'py-4 min-h-[56px]' : 'py-3'} px-6 bg-green-600 hover:bg-green-700 active:bg-green-800 text-white rounded-xl font-semibold text-lg flex items-center justify-center gap-3 transition-colors shadow-lg touch-manipulation`}
                style={{ WebkitTapHighlightColor: 'transparent' }}
              >
                🔒 Secure
              </button>
            )}

            {/* Camera Car Actions */}
            {isCameraCarDriver && (
              <>
                <button
                  onClick={() => handleCameraCheckIn('located')}
                  className={`${isMobile ? 'py-4 min-h-[56px]' : 'py-3'} px-6 bg-green-600 hover:bg-green-700 active:bg-green-800 text-white rounded-xl font-semibold text-lg flex items-center justify-center gap-3 transition-colors shadow-lg touch-manipulation`}
                  style={{ WebkitTapHighlightColor: 'transparent' }}
                >
                  ✅ Located
                </button>

                <button
                  onClick={() => handleCameraCheckIn('not_present')}
                  className={`${isMobile ? 'py-4 min-h-[56px]' : 'py-3'} px-6 bg-yellow-600 hover:bg-yellow-700 active:bg-yellow-800 text-white rounded-xl font-semibold text-lg flex items-center justify-center gap-3 transition-colors shadow-lg touch-manipulation`}
                  style={{ WebkitTapHighlightColor: 'transparent' }}
                >
                  ⏰ Not Present
                </button>

                <button
                  onClick={() => handleCameraCheckIn('blocked')}
                  className={`${isMobile ? 'py-4 min-h-[56px]' : 'py-3'} px-6 bg-orange-600 hover:bg-orange-700 active:bg-orange-800 text-white rounded-xl font-semibold text-lg flex items-center justify-center gap-3 transition-colors shadow-lg touch-manipulation`}
                  style={{ WebkitTapHighlightColor: 'transparent' }}
                >
                  🚧 Blocked In
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderCardModal;
