// StandaloneVehicleTracker.js - ENHANCED VERSION: Added Orders Support for Map Display

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { collection, getDocs, doc, getDoc, query, orderBy, setDoc, updateDoc, serverTimestamp, addDoc, deleteDoc, onSnapshot, where } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Import Team Vehicle Tracker Component
import TeamVehicleTracker from './TeamVehicleTracker';

// Import Vehicle Map Display Component
import VehicleMapDisplay from './VehicleMapDisplay';

// Import Order Card Modal Component
import OrderCardModal from './OrderCardModal';

// Import Statistics Module
import { 
  calculateWeeklyStats, 
  calculateMonthAndYTDStats, 
  StatsCards, 
  MonthlyAndYTDStats 
} from './VehicleTrackerStats';

// Import utilities
import {
  loadPDFLibraries,
  getUserIdFromUrl,
  getUserAvatar,
  getCurrentWeekId,
  formatDateRange,
  formatNumber,
  getStatusColor,
  getStatusEmoji,
  getNotificationColor,
  playNotificationSound,
  playMoneySound,
  playReminderSound,
  compressImage,
  getGeolocation,
  navigateToAddress,
  calculateElapsedTime,
  getTimerDisplay,
  checkUserPermissions,
  suppressGoogleAPIErrors
} from './VehicleTrackerUtils';

// Import Firebase operations
import {
  initializeFirebase,
  loadUserData,
  findUserTeam,
  createWeekIfNeeded,
  initializeWeekWithCarryOvers,
  updateVehicleWeekStats,
  markVINAsSecuredAcrossTeam,
  handleImageUpload,
  loadNeverSecuredVehicles,
  markTeamVehicleBottomStatus,
  recheckTeamVehicle
} from './VehicleTrackerFirebase';

// Import Slack integration
import { postVehicleToSlack, formatVehicleForSlack } from './slackIntegration';

// Enhanced viewport initialization for mobile
const initializeViewport = () => {
  // Remove any existing viewport meta tags
  const existingViewport = document.querySelector('meta[name="viewport"]');
  if (existingViewport) {
    existingViewport.remove();
  }

  // Create new viewport meta tag with proper mobile settings
  const viewport = document.createElement('meta');
  viewport.name = 'viewport';
  viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover';
  document.head.appendChild(viewport);

  // Add mobile web app capable meta tags
  const mobileCapable = document.createElement('meta');
  mobileCapable.name = 'mobile-web-app-capable';
  mobileCapable.content = 'yes';
  document.head.appendChild(mobileCapable);

  const appleCapable = document.createElement('meta');
  appleCapable.name = 'apple-mobile-web-app-capable';
  appleCapable.content = 'yes';
  document.head.appendChild(appleCapable);

  const statusBar = document.createElement('meta');
  statusBar.name = 'apple-mobile-web-app-status-bar-style';
  statusBar.content = 'black-translucent';
  document.head.appendChild(statusBar);

  // Prevent zoom on input focus for iOS
  const handleTouchStart = (e) => {
    if (e.target.nodeName === 'INPUT' || e.target.nodeName === 'TEXTAREA') {
      e.target.style.fontSize = '16px';
    }
  };
  document.addEventListener('touchstart', handleTouchStart);

  // Add mobile-specific styles
  const style = document.createElement('style');
  style.innerHTML = `
    * {
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
    }
    
    body {
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
      text-size-adjust: 100%;
      overscroll-behavior: contain;
    }
    
    input, textarea, select {
      font-size: 16px !important;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }
    
    input[type="date"] {
      min-height: 44px;
    }
    
    /* Prevent horizontal scroll */
    html, body {
      overflow-x: hidden;
      position: relative;
    }
    
    /* Ensure all containers respect viewport */
    .container {
      max-width: 100vw;
      overflow-x: hidden;
    }
    
    /* Fix for iOS input zoom */
    @media screen and (-webkit-min-device-pixel-ratio: 0) {
      input:focus,
      textarea:focus,
      select:focus {
        font-size: 16px !important;
      }
    }
    
    /* Ensure modals work on mobile */
    .fixed {
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
    }
  `;
  document.head.appendChild(style);
};

// Initialize viewport and libraries on load
initializeViewport();
loadPDFLibraries();

// Common vehicle colors
const VEHICLE_COLORS = [
  'Black', 'White', 'Silver', 'Gray', 'Red', 'Blue', 'Green', 
  'Yellow', 'Orange', 'Brown', 'Gold', 'Beige', 'Tan', 'Burgundy',
  'Purple', 'Pink', 'Teal', 'Navy', 'Charcoal', 'Pearl White',
  'Metallic Silver', 'Dark Blue', 'Light Blue', 'Dark Gray'
];

// DO NOT SECURE reasons
const DO_NOT_SECURE_REASONS = [
  'DO NOT USE LIST',
  'BLOCKED IN',
  'PEOPLE IN VEHICLE',
  'OTHER'
];

// STATE OPTIONS FOR DROPDOWN
const STATE_OPTIONS = [
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'MI', label: 'Michigan' },
  { value: 'IA', label: 'Iowa' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'KY', label: 'Kentucky' }
];

// BUILD FULL ADDRESS HELPER
const buildFullAddress = (address, city, state, zipCode) => {
  const parts = [];
  if (address) parts.push(address);
  if (city) parts.push(city);
  if (state) parts.push(state);
  if (zipCode) parts.push(zipCode);
  return parts.join(', ');
};

// WORKING Auto-detect drive type based on vehicle year, make, and model
const autoDetectDriveType = (vehicleName) => {
  if (!vehicleName || typeof vehicleName !== 'string') {
    return 'FWD'; // Default fallback
  }

  const name = vehicleName.toLowerCase().trim();
  
  // First, check for explicit drive type mentions in the name
  if (name.includes('awd') || name.includes('all wheel') || name.includes('all-wheel')) return 'AWD';
  if (name.includes('4wd') || name.includes('4x4') || name.includes('four wheel') || name.includes('4-wheel')) return '4WD';
  if (name.includes('rwd') || name.includes('rear wheel')) return 'RWD';
  if (name.includes('fwd') || name.includes('front wheel')) return 'FWD';
  
  // Extract year if present (4 digits)
  const yearMatch = name.match(/\b(19|20)\d{2}\b/);
  const year = yearMatch ? parseInt(yearMatch[0]) : null;
  
  // TRUCKS & LARGE SUVs - Often RWD with 4WD option
  if (name.includes('f-150') || name.includes('f150')) return name.includes('4wd') || name.includes('4x4') || name.includes('raptor') || name.includes('tremor') ? '4WD' : 'RWD';
  if (name.includes('f-250') || name.includes('f250')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('f-350') || name.includes('f350')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('silverado')) return name.includes('4wd') || name.includes('4x4') || name.includes('z71') || name.includes('trail boss') || name.includes('zr2') ? '4WD' : 'RWD';
  if (name.includes('sierra')) return name.includes('4wd') || name.includes('4x4') || name.includes('at4') ? '4WD' : 'RWD';
  if (name.includes('ram 1500') || name.includes('ram 2500') || name.includes('ram 3500')) return name.includes('4wd') || name.includes('4x4') || name.includes('rebel') || name.includes('trx') ? '4WD' : 'RWD';
  if (name.includes('tundra')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('titan')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('tacoma')) return name.includes('4wd') || name.includes('4x4') || name.includes('trd') ? '4WD' : 'RWD';
  if (name.includes('ranger')) return name.includes('4wd') || name.includes('4x4') || name.includes('raptor') ? '4WD' : 'RWD';
  if (name.includes('colorado')) return name.includes('4wd') || name.includes('4x4') || name.includes('z71') || name.includes('zr2') ? '4WD' : 'RWD';
  if (name.includes('canyon')) return name.includes('4wd') || name.includes('4x4') || name.includes('at4') ? '4WD' : 'RWD';
  if (name.includes('ridgeline')) return 'AWD';
  if (name.includes('frontier')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('gladiator')) return '4WD';
  if (name.includes('maverick')) return name.includes('awd') || name.includes('tremor') ? 'AWD' : 'FWD';
  
  // LARGE SUVs
  if (name.includes('tahoe') || name.includes('suburban') || name.includes('yukon') || name.includes('escalade')) {
    return name.includes('4wd') || name.includes('4x4') || name.includes('z71') ? '4WD' : 'RWD';
  }
  if (name.includes('expedition') || name.includes('navigator')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('sequoia') || name.includes('land cruiser') || name.includes('lx570') || name.includes('lx600')) return '4WD';
  if (name.includes('armada') || name.includes('qx80')) return name.includes('4wd') || name.includes('4x4') ? '4WD' : 'RWD';
  if (name.includes('durango')) return name.includes('awd') || name.includes('4wd') ? 'AWD' : 'RWD';
  
  // JEEP - Most are 4WD
  if (name.includes('jeep')) {
    if (name.includes('compass') || name.includes('renegade')) return name.includes('4wd') || name.includes('4x4') || name.includes('trailhawk') ? '4WD' : 'FWD';
    if (name.includes('cherokee') && !name.includes('grand')) return name.includes('4wd') || name.includes('4x4') || name.includes('trailhawk') ? '4WD' : 'FWD';
    return '4WD'; // Wrangler, Grand Cherokee, etc.
  }
  
  // BRONCO
  if (name.includes('bronco')) {
    if (name.includes('sport')) return name.includes('awd') ? 'AWD' : 'FWD';
    return '4WD'; // Full-size Bronco
  }
  
  // PERFORMANCE/SPORTS CARS - Usually RWD
  if (name.includes('mustang')) {
    if (name.includes('mach-e')) return name.includes('awd') || name.includes('gt') ? 'AWD' : 'RWD';
    return 'RWD';
  }
  if (name.includes('camaro') || name.includes('challenger') || name.includes('charger')) {
    return name.includes('awd') ? 'AWD' : 'RWD';
  }
  if (name.includes('corvette') || name.includes('viper') || name.includes('gt500') || name.includes('hellcat')) return 'RWD';
  if (name.includes('supra') || name.includes('370z') || name.includes('350z')) return 'RWD';
  if (name.includes('miata') || name.includes('mx-5') || name.includes('mx5')) return 'RWD';
  
  // MAINSTREAM BRANDS
  
  // Toyota
  if (name.includes('toyota')) {
    if (name.includes('corolla') || name.includes('camry') || name.includes('prius') || name.includes('yaris')) return 'FWD';
    if (name.includes('avalon') || name.includes('sienna')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('highlander') || name.includes('rav4') || name.includes('venza')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('4runner')) return '4WD';
    if (name.includes('gr86') || name.includes('86')) return 'RWD';
    return 'FWD';
  }
  
  // Honda
  if (name.includes('honda')) {
    if (name.includes('civic') || name.includes('accord') || name.includes('fit') || name.includes('insight')) return 'FWD';
    if (name.includes('cr-v') || name.includes('crv') || name.includes('hr-v') || name.includes('hrv')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('pilot') || name.includes('passport')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('ridgeline')) return 'AWD';
    if (name.includes('s2000')) return 'RWD';
    return 'FWD';
  }
  
  // Nissan
  if (name.includes('nissan')) {
    if (name.includes('sentra') || name.includes('altima') || name.includes('maxima') || name.includes('versa')) return 'FWD';
    if (name.includes('rogue') || name.includes('murano') || name.includes('pathfinder')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('armada') || name.includes('titan')) return name.includes('4wd') ? '4WD' : 'RWD';
    if (name.includes('370z') || name.includes('350z')) return 'RWD';
    if (name.includes('gtr') || name.includes('gt-r')) return 'AWD';
    return 'FWD';
  }
  
  // Ford
  if (name.includes('ford')) {
    if (name.includes('focus') || name.includes('fusion') || name.includes('fiesta')) return 'FWD';
    if (name.includes('escape') || name.includes('edge') || name.includes('explorer')) return name.includes('awd') || name.includes('4wd') ? 'AWD' : 'FWD';
    if (name.includes('bronco')) return '4WD';
    return 'FWD';
  }
  
  // Chevrolet
  if (name.includes('chevrolet') || name.includes('chevy')) {
    if (name.includes('malibu') || name.includes('cruze') || name.includes('spark') || name.includes('sonic')) return 'FWD';
    if (name.includes('impala')) return year && year < 1996 ? 'RWD' : 'FWD';
    if (name.includes('equinox') || name.includes('traverse') || name.includes('blazer')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('trailblazer')) return name.includes('awd') ? 'AWD' : 'FWD';
    return 'FWD';
  }
  
  // GMC
  if (name.includes('gmc')) {
    if (name.includes('terrain') || name.includes('acadia')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('yukon')) return name.includes('4wd') ? '4WD' : 'RWD';
    return 'RWD';
  }
  
  // Dodge
  if (name.includes('dodge')) {
    if (name.includes('charger') || name.includes('challenger')) return name.includes('awd') ? 'AWD' : 'RWD';
    if (name.includes('durango')) return name.includes('awd') || name.includes('4wd') ? 'AWD' : 'RWD';
    if (name.includes('journey')) return name.includes('awd') ? 'AWD' : 'FWD';
    return 'FWD';
  }
  
  // RAM
  if (name.includes('ram')) {
    return name.includes('4wd') || name.includes('4x4') || name.includes('rebel') || name.includes('trx') ? '4WD' : 'RWD';
  }
  
  // Chrysler
  if (name.includes('chrysler')) {
    if (name.includes('300')) return name.includes('awd') ? 'AWD' : 'RWD';
    if (name.includes('pacifica')) return name.includes('awd') ? 'AWD' : 'FWD';
    return 'FWD';
  }
  
  // Cadillac
  if (name.includes('cadillac')) {
    if (name.includes('escalade')) return name.includes('4wd') || name.includes('awd') ? '4WD' : 'RWD';
    if (name.includes('xt4') || name.includes('xt5') || name.includes('xt6')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('ct4') || name.includes('ct5') || name.includes('cts')) return name.includes('awd') ? 'AWD' : 'RWD';
    return 'RWD';
  }
  
  // Lincoln
  if (name.includes('lincoln')) {
    if (name.includes('navigator')) return name.includes('4wd') ? '4WD' : 'RWD';
    if (name.includes('aviator') || name.includes('corsair') || name.includes('nautilus')) return name.includes('awd') ? 'AWD' : 'FWD';
    return 'FWD';
  }
  
  // Buick
  if (name.includes('buick')) {
    if (name.includes('encore') || name.includes('envision') || name.includes('enclave')) return name.includes('awd') ? 'AWD' : 'FWD';
    return 'FWD';
  }
  
  // Hyundai
  if (name.includes('hyundai')) {
    if (name.includes('elantra') || name.includes('sonata') || name.includes('accent')) return 'FWD';
    if (name.includes('tucson') || name.includes('santa fe') || name.includes('kona') || name.includes('palisade')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('veloster')) return 'FWD';
    if (name.includes('genesis coupe')) return 'RWD';
    return 'FWD';
  }
  
  // Kia
  if (name.includes('kia')) {
    if (name.includes('forte') || name.includes('optima') || name.includes('rio') || name.includes('soul')) return 'FWD';
    if (name.includes('sportage') || name.includes('sorento') || name.includes('seltos') || name.includes('telluride')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('stinger')) return name.includes('awd') ? 'AWD' : 'RWD';
    return 'FWD';
  }
  
  // Mazda
  if (name.includes('mazda')) {
    if (name.includes('mazda3') || name.includes('mazda 3')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('mazda6') || name.includes('mazda 6')) return 'FWD';
    if (name.includes('cx-3') || name.includes('cx3') || name.includes('cx-30') || name.includes('cx30')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('cx-5') || name.includes('cx5') || name.includes('cx-9') || name.includes('cx9')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('miata') || name.includes('mx-5') || name.includes('mx5')) return 'RWD';
    return 'FWD';
  }
  
  // Subaru - Almost all AWD
  if (name.includes('subaru')) {
    if (name.includes('brz')) return 'RWD';
    return 'AWD'; // Everything else is AWD
  }
  
  // Volkswagen
  if (name.includes('volkswagen') || name.includes('vw')) {
    if (name.includes('golf') || name.includes('jetta') || name.includes('passat') || name.includes('beetle')) {
      return name.includes('4motion') || name.includes('r32') || name.includes('golf r') ? 'AWD' : 'FWD';
    }
    if (name.includes('tiguan') || name.includes('atlas')) return name.includes('4motion') ? 'AWD' : 'FWD';
    if (name.includes('touareg')) return 'AWD';
    return 'FWD';
  }
  
  // Tesla
  if (name.includes('tesla')) {
    if (name.includes('model s') || name.includes('model x')) return name.includes('long range') || name.includes('plaid') ? 'AWD' : 'RWD';
    if (name.includes('model 3') || name.includes('model y')) return name.includes('long range') || name.includes('performance') ? 'AWD' : 'RWD';
    return 'RWD';
  }
  
  // BMW - xDrive = AWD
  if (name.includes('bmw')) {
    if (name.includes('xdrive') || name.includes('xi') || name.includes('ix')) return 'AWD';
    if (name.includes('x1') || name.includes('x2') || name.includes('x3') || name.includes('x4') || name.includes('x5') || name.includes('x6') || name.includes('x7')) return 'AWD';
    return 'RWD'; // Default for BMW
  }
  
  // Mercedes - 4MATIC = AWD
  if (name.includes('mercedes') || name.includes('benz')) {
    if (name.includes('4matic') || name.includes('4-matic')) return 'AWD';
    if (name.includes('g-class') || name.includes('g-wagon') || name.includes('g63') || name.includes('g550')) return '4WD';
    if (name.includes('gla') || name.includes('glb') || name.includes('glc') || name.includes('gle') || name.includes('gls')) {
      return name.includes('4matic') ? 'AWD' : 'RWD';
    }
    return 'RWD'; // Default for Mercedes
  }
  
  // Audi - Quattro = AWD
  if (name.includes('audi')) {
    if (name.includes('quattro')) return 'AWD';
    if (name.includes('q3') || name.includes('q5') || name.includes('q7') || name.includes('q8')) return 'AWD';
    if (name.includes('rs') || name.includes('s3') || name.includes('s4') || name.includes('s5') || name.includes('s6') || name.includes('s7') || name.includes('s8')) return 'AWD';
    return 'FWD'; // A3, A4 base models
  }
  
  // Lexus
  if (name.includes('lexus')) {
    if (name.includes('gx') || name.includes('lx')) return '4WD';
    if (name.includes('nx') || name.includes('rx') || name.includes('ux')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('is') || name.includes('gs') || name.includes('ls') || name.includes('rc')) return name.includes('awd') ? 'AWD' : 'RWD';
    return 'RWD'; // Default for Lexus sedans
  }
  
  // Infiniti
  if (name.includes('infiniti') || name.includes('qx')) {
    if (name.includes('qx50') || name.includes('qx55') || name.includes('qx60')) return name.includes('awd') ? 'AWD' : 'FWD';
    if (name.includes('qx70') || name.includes('qx80')) return name.includes('awd') || name.includes('4wd') ? '4WD' : 'RWD';
    if (name.includes('q50') || name.includes('q60') || name.includes('q70')) return name.includes('awd') ? 'AWD' : 'RWD';
    return 'RWD';
  }
  
  // Acura
  if (name.includes('acura')) {
    if (name.includes('mdx') || name.includes('rdx')) return 'AWD';
    if (name.includes('tlx') && (name.includes('sh-awd') || name.includes('shawd'))) return 'AWD';
    if (name.includes('nsx')) return 'AWD';
    return 'FWD';
  }
  
  // Land Rover/Range Rover - All 4WD/AWD
  if (name.includes('land rover') || name.includes('range rover')) return '4WD';
  
  // Genesis
  if (name.includes('genesis')) {
    if (name.includes('gv70') || name.includes('gv80')) return name.includes('awd') ? 'AWD' : 'RWD';
    if (name.includes('g70') || name.includes('g80') || name.includes('g90')) return name.includes('awd') ? 'AWD' : 'RWD';
    return 'RWD';
  }
  
  // Generic patterns for any remaining vehicles
  if (name.includes('truck') || name.includes('pickup')) {
    return 'RWD'; // Most trucks are RWD by default
  }
  
  if (name.includes('suv') && (name.includes('full size') || name.includes('large'))) {
    return 'RWD'; // Large SUVs often RWD
  }
  
  if (name.includes('van') || name.includes('minivan')) return 'FWD';
  
  // Default fallback
  return 'FWD';
};

// Get possible drive types info for a vehicle
const getDriveTypeInfo = (vehicleName) => {
  const name = vehicleName.toLowerCase().trim();
  
  // Extract vehicle make/model info
  let info = {
    detectedType: autoDetectDriveType(vehicleName),
    possibleTypes: [],
    subModels: []
  };
  
  // TRUCKS
  if (name.includes('f-150') || name.includes('f150') || name.includes('silverado') || name.includes('sierra') || name.includes('ram')) {
    info.possibleTypes = ['RWD', '4WD'];
    info.subModels = ['Base/Work Truck: RWD', 'Z71/4x4: 4WD', 'Raptor/TRX: 4WD'];
  }
  
  // Toyota/Honda SUVs
  else if (name.includes('rav4') || name.includes('cr-v') || name.includes('highlander')) {
    info.possibleTypes = ['FWD', 'AWD'];
    info.subModels = ['Base: FWD', 'AWD models: AWD'];
  }
  
  // Luxury Sedans
  else if (name.includes('bmw') && (name.includes('3') || name.includes('5'))) {
    info.possibleTypes = ['RWD', 'AWD'];
    info.subModels = ['328i/330i: RWD', '330xi/xDrive: AWD'];
  }
  
  // Subaru
  else if (name.includes('subaru')) {
    if (name.includes('brz')) {
      info.possibleTypes = ['RWD'];
      info.subModels = ['BRZ: RWD only'];
    } else {
      info.possibleTypes = ['AWD'];
      info.subModels = ['All models: AWD standard'];
    }
  }
  
  // Performance cars
  else if (name.includes('mustang') || name.includes('camaro') || name.includes('challenger')) {
    info.possibleTypes = ['RWD', 'AWD'];
    info.subModels = ['Base/GT: RWD', 'AWD (if available): AWD'];
  }
  
  // Default for most cars
  else {
    info.possibleTypes = [info.detectedType];
    info.subModels = [`Standard: ${info.detectedType}`];
  }
  
  return info;
};

function StandaloneVehicleTracker() {
  const [db, setDb] = useState(null);
  const [storage, setStorage] = useState(null);
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [team, setTeam] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [initError, setInitError] = useState(null);

  // Vehicle tracking state
  const [vehicles, setVehicles] = useState([]);
  const [selectedWeek, setSelectedWeek] = useState(null);
  const [availableWeeks, setAvailableWeeks] = useState([]);
  const [vehicleStats, setVehicleStats] = useState({
    totalScans: 0,
    totalFound: 0,
    totalSecured: 0,
    recoveryRate: 0,
    dateRange: { start: '', end: '' }
  });

  // NEW: Vehicle selection state for map integration
  const [selectedVehicleId, setSelectedVehicleId] = useState(null);
  const [teamVehicles, setTeamVehicles] = useState([]);

  // NEW: Order management state
  const [orders, setOrders] = useState([]);
  const [selectedOrderId, setSelectedOrderId] = useState(null);
  const [ordersLoading, setOrdersLoading] = useState(false);

  // Enhanced form state with VIN verification, images, color, and DO NOT SECURE
  const [showAddForm, setShowAddForm] = useState(false);
  const [showDoNotSecureReason, setShowDoNotSecureReason] = useState(false);
  const [doNotSecureReason, setDoNotSecureReason] = useState('');
  const [customDoNotSecureReason, setCustomDoNotSecureReason] = useState('');
  
  // Load saved form data from localStorage on mount
  const getInitialFormData = () => {
    const saved = localStorage.getItem('vehicleFormDraft');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        // Ensure date is current if it's old
        if (parsed.date && new Date(parsed.date) < new Date(new Date().setHours(0,0,0,0))) {
          parsed.date = new Date().toISOString().split('T')[0];
        }
        return parsed;
      } catch (e) {
        console.error('Error loading saved form:', e);
      }
    }
    return {
      vehicle: '',
      vin: '',
      vinVerified: false,
      status: 'PENDING PICKUP',
      date: new Date().toISOString().split('T')[0],
      plateNumber: '',
      accountNumber: '',
      financier: '',
      address: '',
      city: '',        // NEW FIELD
      state: '',       // NEW FIELD
      zipCode: '',     // NEW FIELD
      position: null,
      notes: '',
      images: [],
      color: '',
      driveType: '',
      doNotSecureReason: ''
    };
  };
  
  const [newVehicle, setNewVehicle] = useState(getInitialFormData());

  // Geolocation state
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [locationError, setLocationError] = useState(null);

  // Image upload state
  const [uploadingImages, setUploadingImages] = useState(false);
  const [imageUploadProgress, setImageUploadProgress] = useState(0);
  const fileInputRef = useRef(null);
  const editFileInputRef = useRef(null);
  const cameraInputRef = useRef(null);
  const editCameraInputRef = useRef(null);

  // Image modal state
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageModal, setShowImageModal] = useState(false);

  // Enhanced editing state with color and drive type
  const [editingVehicle, setEditingVehicle] = useState(null);
  const [editVehicleData, setEditVehicleData] = useState({});
  const [editUploadingImages, setEditUploadingImages] = useState(false);
  const [editImageUploadProgress, setEditImageUploadProgress] = useState(0);
  const [isGettingEditLocation, setIsGettingEditLocation] = useState(false);

  // Notes editing state
  const [editingNotes, setEditingNotes] = useState(null);
  const [tempNotes, setTempNotes] = useState('');

  // Scan editing state
  const [editingScans, setEditingScans] = useState(false);
  const [editScanAmount, setEditScanAmount] = useState(0);

  // YTD and statistics
  const [lifetimeSecuredCount, setLifetimeSecuredCount] = useState(0);
  const [ytdStats, setYtdStats] = useState({
    totalFound: 0,
    totalSecured: 0,
    totalScans: 0,
    recoveryRate: 0,
    month: {
      totalFound: 0,
      totalSecured: 0,
      totalScans: 0,
      recoveryRate: 0
    }
  });

  // Team sync notifications
  const [teamSyncNotifications, setTeamSyncNotifications] = useState([]);

  // Sound notification settings
  const [soundEnabled, setSoundEnabled] = useState(true);
  const notificationSoundRef = useRef(null);
  const moneySoundRef = useRef(null);

  // Print functionality
  const [showPrintView, setShowPrintView] = useState(false);
  const printRef = useRef(null);

  // Viewport width detection for responsive design
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [showActions, setShowActions] = useState({});

  // User permissions
  const [canSecureVehicles, setCanSecureVehicles] = useState(false);

  // NEW: Vehicle selection callback - called when vehicle is selected from map or team tracker
  const handleVehicleSelect = useCallback((vehicleUniqueKey) => {
    console.log('🎯 Vehicle selected:', vehicleUniqueKey);
    setSelectedVehicleId(vehicleUniqueKey);
  }, []);

  // NEW: Order selection callback
  const handleOrderSelect = useCallback((orderId) => {
    console.log('📋 Order selected:', orderId);
    setSelectedOrderId(orderId);
  }, []);

  // NEW: Order card modal state
  const [showOrderCard, setShowOrderCard] = useState(false);
  const [selectedOrderForCard, setSelectedOrderForCard] = useState(null);

  // NEW: Handle order card display
  const handleOrderCardDisplay = useCallback((order) => {
    console.log('🎯 Showing order card for:', order.id);
    setSelectedOrderForCard(order);
    setShowOrderCard(true);
  }, []);

  // NEW: Team tracker visibility state
  const [showTeamTracker, setShowTeamTracker] = useState(true);

  // NEW: Order navigation callback
  const handleOrderNavigate = useCallback((order) => {
    console.log('🗺️ Navigating to order:', order.id);
    if (order.position || (order.addresses && order.addresses.length > 0)) {
      const address = order.addresses?.[0];
      let coords = order.position;
      
      if (!coords && address?.position) {
        coords = address.position;
      }
      
      if (coords) {
        // Open navigation in default maps app
        const url = `https://www.google.com/maps/dir/?api=1&destination=${coords.lat},${coords.lng}`;
        window.open(url, '_blank');
      } else {
        // Fallback to address string
        const addressString = typeof address === 'string' ? address : 
          `${address?.street || ''} ${address?.city || ''} ${address?.state || ''} ${address?.zip || ''}`.trim();
        if (addressString) {
          const url = `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(addressString)}`;
          window.open(url, '_blank');
        }
      }
    }
  }, []);

  // NEW: Order secure callback
  const handleOrderSecure = useCallback(async (order) => {
    console.log('🔒 Securing order:', order.id);
    if (!db || !user) return;

    try {
      // Update the order status to secured
      await updateDoc(doc(db, 'orders', order.id), {
        status: 'secure',
        secure: true,
        securedBy: user.displayName || user.email,
        secureTimestamp: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // Also update in locations collection if it exists
      const locationRef = doc(db, 'locations', order.id);
      const locationDoc = await getDoc(locationRef);
      if (locationDoc.exists()) {
        await updateDoc(locationRef, {
          status: 'secured',
          updatedAt: serverTimestamp()
        });
      }

      // Show success message
      alert(`Successfully secured order: ${order.year} ${order.make} ${order.model}`);
      
      // Play money sound
      playMoneySound(soundEnabled);
      
      // Reload orders
      loadOrders();
    } catch (error) {
      console.error('Error securing order:', error);
      alert('Failed to secure order. Please try again.');
    }
  }, [db, user, soundEnabled]);

  // NEW: Load orders from Firestore
  const loadOrders = useCallback(async () => {
    if (!db || !team) return;

    try {
      setOrdersLoading(true);
      console.log('📋 Loading orders for team:', team.id);

      // Query for open orders only (to reduce map clutter)
      const ordersQuery = query(
        collection(db, 'orders'),
        where('teamId', '==', team.id),
        where('status', 'in', ['open', 'pending', 'open-order', 'pending-pickup']),
        orderBy('createdAt', 'desc')
      );

      const unsubscribe = onSnapshot(ordersQuery, (snapshot) => {
        const ordersData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log(`📋 Loaded ${ordersData.length} open orders for map display`);
        setOrders(ordersData);
        setOrdersLoading(false);
      }, (error) => {
        console.error('❌ Error loading orders:', error);
        setOrders([]);
        setOrdersLoading(false);
      });

      return unsubscribe;
    } catch (error) {
      console.error('❌ Error setting up orders listener:', error);
      setOrders([]);
      setOrdersLoading(false);
    }
  }, [db, team]);

  // Handle vehicle deleted from map
  const handleVehicleDeletedFromMap = (deletedVehicle) => {
    console.log('🗑️ Vehicle deleted from map:', deletedVehicle.vehicle);
    
    // Remove from team vehicles state
    setTeamVehicles(prev => prev.filter(v => 
      (v.uniqueKey || v.id) !== (deletedVehicle.uniqueKey || deletedVehicle.id)
    ));
    
    // Show notification
    const notification = {
      id: Date.now(),
      type: 'admin_delete',
      message: `🗑️ Admin moved ${deletedVehicle.vehicle} (VIN: ${deletedVehicle.vin}) to Never Secured`,
      timestamp: new Date()
    };

    setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
    setTimeout(() => {
      setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, 8000);
  };

  // Initialize PDF libraries on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      loadPDFLibraries();
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  // Suppress Google API errors on mobile
  useEffect(() => {
    suppressGoogleAPIErrors();
  }, []);

  // Reverse geocoding function
  const reverseGeocodeCoordinates = async (lat, lng) => {
    try {
      console.log(`Reverse geocoding coordinates: ${lat}, ${lng}`);
      
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'VehicleTracker/1.0'
          }
        }
      );
      
      if (!response.ok) {
        throw new Error(`Reverse geocoding API error: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && data.address) {
        const addr = data.address;
        let streetAddress = '';
        let city = '';
        let state = '';
        let zipCode = '';
        
        // Build street address
        if (addr.house_number) streetAddress += addr.house_number + ' ';
        if (addr.road) streetAddress += addr.road;
        else if (addr.street) streetAddress += addr.street;
        
        // Get city
        city = addr.city || addr.town || addr.village || addr.suburb || '';
        
        // Get state
        state = addr.state || '';
        
        // Get zip code
        zipCode = addr.postcode || '';
        
        return {
          streetAddress: streetAddress.trim(),
          city: city,
          state: state,
          zipCode: zipCode,
          fullAddress: data.display_name
        };
      }
      
      return null;
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return null;
    }
  };

  // Get current location and address for new vehicle
  const getCurrentLocationAndAddress = async () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by your browser');
      return;
    }
    
    setIsGettingLocation(true);
    setLocationError(null);
    
    try {
      const position = await new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          (position) => resolve(position),
          (error) => reject(error),
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0
          }
        );
      });
      
      const { latitude, longitude } = position.coords;
      const coordinates = { lat: latitude, lng: longitude };
      
      console.log('Got current location:', coordinates);
      
      // Get the address components
      const addressData = await reverseGeocodeCoordinates(latitude, longitude);
      
      if (addressData) {
        setNewVehicle(prev => {
          const updated = {
            ...prev,
            position: coordinates,
            address: addressData.streetAddress,
            city: addressData.city,
            state: addressData.state,
            zipCode: addressData.zipCode
          };
          // Auto-save to localStorage
          localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
          return updated;
        });
        
        console.log('Location and address captured:', { coordinates, addressData });
        playNotificationSound(soundEnabled);
        
      } else {
        setNewVehicle(prev => {
          const updated = {
            ...prev,
            position: coordinates,
            address: '',
            city: '',
            state: '',
            zipCode: ''
          };
          // Auto-save to localStorage
          localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
          return updated;
        });
        alert('Location captured but could not determine address. You can enter it manually.');
      }
    } catch (error) {
      console.error('Error getting location:', error);
      
      let errorMessage = 'Failed to get your location. ';
      if (error.code === 1) {
        errorMessage += 'Please allow location access and try again.';
      } else if (error.code === 2) {
        errorMessage += 'Location information is unavailable.';
      } else if (error.code === 3) {
        errorMessage += 'Location request timed out.';
      } else {
        errorMessage += error.message || 'Please try again.';
      }
      
      setLocationError(errorMessage);
      alert(errorMessage);
    } finally {
      setIsGettingLocation(false);
    }
  };

  // Get current location and address for edit vehicle
  const getCurrentLocationAndAddressForEdit = async () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by your browser');
      return;
    }
    
    setIsGettingEditLocation(true);
    
    try {
      const position = await new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          (position) => resolve(position),
          (error) => reject(error),
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0
          }
        );
      });
      
      const { latitude, longitude } = position.coords;
      const coordinates = { lat: latitude, lng: longitude };
      
      console.log('Got current location for edit:', coordinates);
      
      // Get the address components
      const addressData = await reverseGeocodeCoordinates(latitude, longitude);
      
      if (addressData) {
        setEditVehicleData(prev => ({
          ...prev,
          position: coordinates,
          address: addressData.streetAddress,
          city: addressData.city,
          state: addressData.state,
          zipCode: addressData.zipCode
        }));
        
        console.log('Edit location and address captured:', { coordinates, addressData });
        playNotificationSound(soundEnabled);
        
      } else {
        setEditVehicleData(prev => ({
          ...prev,
          position: coordinates
        }));
        alert('Location captured but could not determine address. You can enter it manually.');
      }
    } catch (error) {
      console.error('Error getting location for edit:', error);
      
      let errorMessage = 'Failed to get your location. ';
      if (error.code === 1) {
        errorMessage += 'Please allow location access and try again.';
      } else if (error.code === 2) {
        errorMessage += 'Location information is unavailable.';
      } else if (error.code === 3) {
        errorMessage += 'Location request timed out.';
      } else {
        errorMessage += error.message || 'Please try again.';
      }
      
      alert(errorMessage);
    } finally {
      setIsGettingEditLocation(false);
    }
  };

  // AGGRESSIVE cleanup function to remove incorrectly added team vehicles from ALL weeks
  const cleanupIncorrectlyAddedVehicles = async () => {
    if (!db || !user) return;

    try {
      console.log('🧹 Starting aggressive cleanup of incorrectly added team vehicles...');
      
      // Get ALL weeks for this user
      const weeksQuery = query(
        collection(db, 'users', user.id, 'vehicleWeeks'),
        orderBy('startDate', 'desc')
      );
      const weeksSnapshot = await getDocs(weeksQuery);
      
      let totalRemovedCount = 0;
      const vinsSeen = new Set(); // Track VINs to prevent duplicates
      
      // Go through EVERY week
      for (const weekDoc of weeksSnapshot.docs) {
        const weekId = weekDoc.id;
        console.log(`Checking week: ${weekId}`);
        
        const vehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', weekId, 'vehicles');
        const snapshot = await getDocs(vehiclesRef);
        
        let weekRemovedCount = 0;
        
        for (const doc of snapshot.docs) {
          const vehicle = doc.data();
          
          // Remove vehicles that:
          // 1. Have teamMemberId that's not the current user
          // 2. Have copiedFromTeammate flag
          // 3. Have fromTeammate flag (old bottom status vehicles)
          // 4. Have bottomStatus but are not secured
          // 5. Are not secured by this user (no securedFromTeammate flag)
          // 6. Have duplicate VINs (keep only the oldest one)
          const shouldRemove = (
            (vehicle.teamMemberId && vehicle.teamMemberId !== user.id && !vehicle.securedFromTeammate) ||
            vehicle.copiedFromTeammate === true ||
            (vehicle.fromTeammate === true && vehicle.status !== 'SECURED') ||
            (vehicle.bottomStatus && vehicle.status !== 'SECURED' && !vehicle.addedBy?.includes(user.email)) ||
            (vehicle.vin && vinsSeen.has(vehicle.vin.toUpperCase()) && vehicle.status !== 'SECURED')
          );
          
          if (shouldRemove) {
            console.log(`Removing vehicle from week ${weekId}: ${vehicle.vehicle} (VIN: ${vehicle.vin})`);
            console.log(`  Reason: teamMemberId=${vehicle.teamMemberId}, copiedFromTeammate=${vehicle.copiedFromTeammate}, fromTeammate=${vehicle.fromTeammate}, bottomStatus=${vehicle.bottomStatus}`);
            await deleteDoc(doc.ref);
            weekRemovedCount++;
          } else if (vehicle.vin) {
            // Track this VIN so we can remove duplicates
            vinsSeen.add(vehicle.vin.toUpperCase());
          }
        }
        
        if (weekRemovedCount > 0) {
          console.log(`✅ Removed ${weekRemovedCount} vehicles from week ${weekId}`);
          totalRemovedCount += weekRemovedCount;
        }
      }
      
      if (totalRemovedCount > 0) {
        console.log(`✅ Total removed: ${totalRemovedCount} incorrectly added vehicles across all weeks`);
        alert(`Cleaned up ${totalRemovedCount} vehicles that were incorrectly added to your list across all weeks.`);
        
        // Reload the current week data
        await loadVehicleDataForWeek(selectedWeek);
      } else {
        console.log('✅ No incorrectly added vehicles found');
        alert('No incorrectly added vehicles found in any week.');
      }
      
    } catch (error) {
      console.error('Error cleaning up vehicles:', error);
      alert('Error during cleanup. Check console for details.');
    }
  };

  // FIXED: Enhanced carryover logic to prevent duplicates
  const carryOverFoundVehicles = async (fromWeekId, toWeekId) => {
    if (!db || !user) return;

    try {
      console.log(`🔄 Carrying over vehicles from ${fromWeekId} to ${toWeekId}`);

      // Get all vehicles from the previous week that are not SECURED
      const prevWeekVehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', fromWeekId, 'vehicles');
      const prevWeekSnapshot = await getDocs(prevWeekVehiclesRef);
      
      // Get current week vehicles to avoid duplicates
      const currentWeekVehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', toWeekId, 'vehicles');
      const currentWeekSnapshot = await getDocs(currentWeekVehiclesRef);
      
      // Create a set of VINs already in current week - CASE INSENSITIVE
      const currentWeekVINs = new Set();
      currentWeekSnapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.vin && data.vin.trim() !== '') {
          currentWeekVINs.add(data.vin.trim().toUpperCase());
        }
      });
      
      console.log(`📋 Current week has ${currentWeekVINs.size} vehicles with VINs`);
      
      const vehiclesToCarryOver = [];
      
      prevWeekSnapshot.docs.forEach(docSnap => {
        const vehicleData = docSnap.data();
        
        // Only carry over if:
        // 1. Not secured
        // 2. Has a VIN
        // 3. VIN is not already in current week (case insensitive check)
        // 4. Is user's own vehicle (not a team vehicle that was incorrectly added)
        const isOwnVehicle = !vehicleData.teamMemberId || vehicleData.teamMemberId === user.id || vehicleData.securedFromTeammate === true;
        
        if (vehicleData.status !== 'SECURED' && 
            vehicleData.vin && 
            vehicleData.vin.trim() !== '' &&
            !currentWeekVINs.has(vehicleData.vin.trim().toUpperCase()) &&
            isOwnVehicle &&
            !vehicleData.copiedFromTeammate &&
            !vehicleData.fromTeammate &&
            !vehicleData.bottomStatus) {
          vehiclesToCarryOver.push({
            ...vehicleData,
            id: docSnap.id
          });
        }
      });

      console.log(`📦 Found ${vehiclesToCarryOver.length} vehicles to carry over`);
      vehiclesToCarryOver.forEach(v => {
        console.log(`  - ${v.vehicle} (${v.vin}) - Status: ${v.status}`);
      });

      // Add each vehicle to the new week as a carryover
      for (const vehicle of vehiclesToCarryOver) {
        // Clean the vehicle data before carrying over
        const carryoverVehicleData = {
          vehicle: vehicle.vehicle,
          vin: vehicle.vin,
          vinVerified: vehicle.vinVerified || false,
          status: vehicle.status,
          date: vehicle.date,
          plateNumber: vehicle.plateNumber || '',
          accountNumber: vehicle.accountNumber || '',
          financier: vehicle.financier || '',
          address: vehicle.address || '',
          city: vehicle.city || '',
          state: vehicle.state || '',
          zipCode: vehicle.zipCode || '',
          position: vehicle.position || null,
          notes: vehicle.notes || '',
          images: vehicle.images || [],
          color: vehicle.color || '',
          driveType: vehicle.driveType || '',
          doNotSecureReason: vehicle.doNotSecureReason || '',
          carriedOver: true,
          originalWeekId: vehicle.originalWeekId || fromWeekId,
          originalId: vehicle.originalId || vehicle.id,
          carriedOverDate: new Date().toISOString().split('T')[0],
          lastCarriedFromWeek: fromWeekId,
          carryoverCount: (vehicle.carryoverCount || 0) + 1,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          addedBy: user.displayName || user.email,
          addedByUserId: user.id
        };

        // Explicitly remove any team-related fields
        delete carryoverVehicleData.teamMemberId;
        delete carryoverVehicleData.teamMemberName;
        delete carryoverVehicleData.copiedFromTeammate;
        delete carryoverVehicleData.fromTeammate;
        delete carryoverVehicleData.bottomStatus;
        delete carryoverVehicleData.bottomStatusCount;
        delete carryoverVehicleData.bottomStatusByUserId;
        delete carryoverVehicleData.bottomStatusByUserName;
        delete carryoverVehicleData.bottomStatusDate;

        await addDoc(currentWeekVehiclesRef, carryoverVehicleData);
        console.log(`✅ Carried over vehicle: ${vehicle.vehicle} (${vehicle.vin})`);
      }

    } catch (error) {
      console.error("❌ Error carrying over vehicles:", error);
    }
  };

  // Manual check for missing carryovers - FIXED
  const checkForMissingCarryovers = async () => {
    if (!db || !user || !selectedWeek || !availableWeeks) return;
    
    try {
      setLoading(true);
      
      // Find the previous week
      const currentWeekIndex = availableWeeks.findIndex(w => w.id === selectedWeek);
      const previousWeek = availableWeeks[currentWeekIndex + 1]; // Since weeks are sorted desc
      
      if (!previousWeek) {
        alert('No previous week found to check for carryovers.');
        return;
      }
      
      console.log(`🔍 Manually checking for carryovers from ${previousWeek.id} to ${selectedWeek}`);
      
      // Get all vehicles from previous week
      const prevWeekVehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', previousWeek.id, 'vehicles');
      const prevWeekSnapshot = await getDocs(prevWeekVehiclesRef);
      
      // Get current week vehicles to check what's already there
      const currentWeekVehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', selectedWeek, 'vehicles');
      const currentWeekSnapshot = await getDocs(currentWeekVehiclesRef);
      
      // Create a set of VINs already in current week - CASE INSENSITIVE
      const currentWeekVINs = new Set();
      currentWeekSnapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.vin && data.vin.trim() !== '') {
          currentWeekVINs.add(data.vin.trim().toUpperCase());
        }
      });
      
      // Find vehicles that should be carried over
      const vehiclesToCarryOver = [];
      prevWeekSnapshot.docs.forEach(docSnap => {
        const vehicleData = docSnap.data();
        
        // Check if this vehicle should be carried over
        if (vehicleData.status !== 'SECURED' && 
            vehicleData.vin && 
            vehicleData.vin.trim() !== '' &&
            !currentWeekVINs.has(vehicleData.vin.trim().toUpperCase())) {
          vehiclesToCarryOver.push({
            ...vehicleData,
            id: docSnap.id
          });
        }
      });
      
      if (vehiclesToCarryOver.length === 0) {
        alert('No missing vehicles found. All pending vehicles have been carried over.');
        return;
      }
      
      const confirmMessage = `Found ${vehiclesToCarryOver.length} missing vehicle(s) from previous week:\n\n` +
        vehiclesToCarryOver.map(v => `- ${v.vehicle} (VIN: ${v.vin}) - ${v.status}`).join('\n') +
        '\n\nDo you want to carry them over to this week?';
      
      if (confirm(confirmMessage)) {
        // Carry over the missing vehicles
        for (const vehicle of vehiclesToCarryOver) {
          const carryoverVehicleData = {
            ...vehicle,
            carriedOver: true,
            originalWeekId: vehicle.originalWeekId || previousWeek.id,
            originalId: vehicle.originalId || vehicle.id,
            carriedOverDate: new Date().toISOString().split('T')[0],
            lastCarriedFromWeek: previousWeek.id,
            carryoverCount: (vehicle.carryoverCount || 0) + 1,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          };
          
          delete carryoverVehicleData.id;
          
          await addDoc(currentWeekVehiclesRef, carryoverVehicleData);
          console.log(`✅ Manually carried over: ${vehicle.vehicle} (${vehicle.vin})`);
        }
        
        alert(`Successfully carried over ${vehiclesToCarryOver.length} vehicle(s) to this week.`);
        
        // Reload the current week data
        await loadVehicleDataForWeek(selectedWeek);
      }
      
    } catch (error) {
      console.error('Error checking for missing carryovers:', error);
      alert('Error checking for missing carryovers. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Get user display name
  const getUserDisplayName = () => {
    if (!user) return "";

    if (user.displayName) {
      return user.displayName.toUpperCase();
    } else if (user.email) {
      return user.email.split('@')[0].toUpperCase();
    }
    return "USER";
  };

  // Get week display name
  const getWeekDisplayName = () => {
    if (!selectedWeek || !availableWeeks) return "CURRENT WEEK";

    const week = availableWeeks.find(w => w.id === selectedWeek);
    return week ? (week.displayRange || "WEEK") : "CURRENT WEEK";
  };

  // Show team sync notification with sound
  const showTeamSyncNotification = (vehicleData) => {
    if (vehicleData.autoSecuredFromTeam && vehicleData.securedByUserName) {
      const notification = {
        id: Date.now(),
        type: 'team_sync',
        message: `🎉 ${vehicleData.securedByUserName} secured ${vehicleData.vehicle} (VIN: ${vehicleData.vin})`,
        timestamp: new Date(),
        vehicleId: vehicleData.id
      };

      playNotificationSound(soundEnabled);

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);

      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 10000);
    }
  };

  // ENHANCED: Load available weeks with automatic carryover
  const loadAvailableWeeks = async () => {
    if (!db || !user) return;

    try {
      setVehicles([]);
      setVehicleStats({
        totalScans: 0,
        totalFound: 0,
        totalSecured: 0,
        recoveryRate: 0,
        dateRange: { start: '', end: '' }
      });

      const weeksQuery = query(
        collection(db, 'users', user.id, 'vehicleWeeks'),
        orderBy('endDate', 'desc')
      );

      const weeksSnapshot = await getDocs(weeksQuery);
      const weeksData = weeksSnapshot.docs.map(doc => {
        const data = doc.data();
        const startDate = data.startDate?.toDate();
        const endDate = data.endDate?.toDate();

        let displayRange = data.displayRange;
        if (startDate && endDate && (!displayRange || displayRange.trim() === '')) {
          displayRange = formatDateRange(startDate, endDate);
        }

        return {
          id: doc.id,
          ...data,
          startDate: startDate,
          endDate: endDate,
          displayRange: displayRange
        };
      });

      setAvailableWeeks(weeksData);

      const today = new Date();
      let currentWeek = weeksData.find(week =>
        week.startDate <= today && week.endDate >= today
      );

      const mostRecentWeek = weeksData.length > 0 ? weeksData[0] : null;

      let weekToSelect = null;
      if (currentWeek) {
        weekToSelect = currentWeek;
      } else if (mostRecentWeek) {
        weekToSelect = mostRecentWeek;
      } else {
        const currentWeekId = getCurrentWeekId();
        await createWeekIfNeeded(db, user.id, currentWeekId);

        const newWeekDoc = await getDoc(doc(db, 'users', user.id, 'vehicleWeeks', currentWeekId));
        if (newWeekDoc.exists()) {
          const newWeekData = newWeekDoc.data();
          weekToSelect = {
            id: currentWeekId,
            ...newWeekData,
            startDate: newWeekData.startDate?.toDate(),
            endDate: newWeekData.endDate?.toDate()
          };
        }
      }

      if (weekToSelect) {
        setSelectedWeek(weekToSelect.id);
        
        // Automatic carryover check
        const isCurrentWeek = currentWeek && weekToSelect.id === currentWeek.id;
        const hasPreviousWeeks = weeksData.length > 1;
        
        if (isCurrentWeek && hasPreviousWeeks) {
          // Find the most recent week that's not the current week
          const previousWeek = weeksData.find(w => w.id !== weekToSelect.id);
          
          if (previousWeek) {
            console.log(`🔍 Checking if carryovers needed from ${previousWeek.id} to ${weekToSelect.id}`);
            
            // Always attempt carryover for current week
            await carryOverFoundVehicles(previousWeek.id, weekToSelect.id);
          }
        }
        
        await initializeWeekWithCarryOvers(db, user.id, weekToSelect.id);
        await loadVehicleDataForWeek(weekToSelect.id);
      }

      // Calculate Month and YTD stats using imported function
      const stats = await calculateMonthAndYTDStats(db, user, vehicles, selectedWeek, vehicleStats);
      setYtdStats(stats);

    } catch (error) {
      console.error("Error loading available weeks:", error);
      setAvailableWeeks([]);
    }
  };

  // Load vehicle data for a specific week with real-time updates
  const loadVehicleDataForWeek = async (weekId) => {
    if (!db || !user || !weekId) return;

    try {
      const weekDoc = await getDoc(doc(db, 'users', user.id, 'vehicleWeeks', weekId));
      if (weekDoc.exists()) {
        const weekData = weekDoc.data();

        let startDateStr = '';
        let endDateStr = '';

        if (weekData.displayRange) {
          const parts = weekData.displayRange.split(' - ');
          startDateStr = parts[0] || '';
          endDateStr = parts[1] || '';
        } else if (weekData.startDate && weekData.endDate) {
          const startDate = weekData.startDate.toDate();
          const endDate = weekData.endDate.toDate();
          const formattedRange = formatDateRange(startDate, endDate);
          const parts = formattedRange.split(' - ');
          startDateStr = parts[0] || '';
          endDateStr = parts[1] || '';

          await updateDoc(doc(db, 'users', user.id, 'vehicleWeeks', weekId), {
            displayRange: formattedRange
          });
        }

        const statsData = {
          totalScans: weekData.totalScans || 0,
          totalFound: weekData.totalFound || 0,
          totalSecured: weekData.totalSecured || 0,
          recoveryRate: weekData.recoveryRate || 0,
          dateRange: {
            start: startDateStr,
            end: endDateStr
          }
        };

        setVehicleStats(statsData);
      }

      const vehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', weekId, 'vehicles');

      const unsubscribe = onSnapshot(vehiclesRef, (snapshot) => {
        const vehiclesData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        // FILTER OUT VEHICLES THAT SHOULDN'T BE IN THIS USER'S LIST
        const filteredVehicles = vehiclesData.filter(vehicle => {
          // Keep vehicles that:
          // 1. Were added by this user (no teamMemberId or teamMemberId matches user.id)
          // 2. Were secured by this user (securedFromTeammate is true)
          // 3. Don't have copiedFromTeammate flag
          // 4. Don't have fromTeammate flag (unless secured)
          // 5. Don't have bottomStatus (unless secured)
          
          const isOwnVehicle = !vehicle.teamMemberId || vehicle.teamMemberId === user.id;
          const isSecuredFromTeammate = vehicle.securedFromTeammate === true;
          const isNotCopiedFromTeammate = !vehicle.copiedFromTeammate;
          const isNotFromTeammate = !vehicle.fromTeammate || vehicle.status === 'SECURED';
          const hasNoBottomStatus = !vehicle.bottomStatus || vehicle.status === 'SECURED';
          
          // Additional check: if vehicle has addedBy field, check if it matches current user
          const addedByCurrentUser = !vehicle.addedBy || vehicle.addedBy === user.email || vehicle.addedBy === user.displayName || vehicle.addedByUserId === user.id;
          
          return ((isOwnVehicle && addedByCurrentUser) || isSecuredFromTeammate) && 
                 isNotCopiedFromTeammate && 
                 isNotFromTeammate && 
                 hasNoBottomStatus;
        });

        const sortedVehicles = filteredVehicles.sort((a, b) => {
          if (a.status === 'SECURED' && b.status !== 'SECURED') return -1;
          if (b.status === 'SECURED' && a.status !== 'SECURED') return 1;

          if (a.carriedOver && !b.carriedOver) return 1;
          if (!a.carriedOver && b.carriedOver) return -1;

          const dateA = new Date(a.securedDate || a.date || '1900-01-01');
          const dateB = new Date(b.securedDate || b.date || '1900-01-01');
          return dateB - dateA;
        });

        filteredVehicles.forEach(vehicle => {
          if (vehicle.autoSecuredFromTeam && vehicle.securedByTeammate && !vehicle.notificationShown) {
            showTeamSyncNotification(vehicle);

            updateDoc(doc(vehiclesRef, vehicle.id), {
              notificationShown: true
            }).catch(err => console.error("Error updating notification flag:", err));
          }
        });

        setVehicles(sortedVehicles);
        updateVehicleWeekStats(db, user.id, weekId);
      });

      return unsubscribe;

    } catch (error) {
      console.error("Error loading vehicle data for week:", error);
      setVehicles([]);
      setVehicleStats({
        totalScans: 0,
        totalFound: 0,
        totalSecured: 0,
        recoveryRate: 0,
        dateRange: { start: '', end: '' }
      });
    }
  };

  // Show image modal
  const showImageInModal = (imageUrl) => {
    setSelectedImage(imageUrl);
    setShowImageModal(true);
  };

  // Handle file selection
  const handleFileSelect = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    setUploadingImages(true);
    setImageUploadProgress(0);

    try {
      const uploadedImages = await handleImageUpload(storage, user, files);

      setNewVehicle(prev => {
        const updated = {
          ...prev,
          images: [...prev.images, ...uploadedImages]
        };
        // Auto-save to localStorage
        localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
        return updated;
      });

      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      if (cameraInputRef.current) {
        cameraInputRef.current.value = '';
      }
    } catch (error) {
      alert(`Failed to upload images: ${error.message}`);
    } finally {
      setUploadingImages(false);
      setImageUploadProgress(0);
    }
  };

  // Handle file selection for edit form
  const handleEditFileSelect = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    setEditUploadingImages(true);
    setEditImageUploadProgress(0);

    try {
      const uploadedImages = await handleImageUpload(storage, user, files);

      setEditVehicleData(prev => ({
        ...prev,
        images: [...(prev.images || []), ...uploadedImages]
      }));

      if (editFileInputRef.current) {
        editFileInputRef.current.value = '';
      }
      if (editCameraInputRef.current) {
        editCameraInputRef.current.value = '';
      }
    } catch (error) {
      alert(`Failed to upload images: ${error.message}`);
    } finally {
      setEditUploadingImages(false);
      setEditImageUploadProgress(0);
    }
  };

  // Remove image from new vehicle
  const removeImageFromNewVehicle = (index) => {
    setNewVehicle(prev => {
      const updated = {
        ...prev,
        images: prev.images.filter((_, i) => i !== index)
      };
      // Auto-save to localStorage
      localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
      return updated;
    });
  };

  // Remove image from edit vehicle
  const removeImageFromEditVehicle = (index) => {
    setEditVehicleData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  // Get proper vehicle count for display
  const getProperVehicleCount = () => {
    return vehicles ? vehicles.filter(v => v.status === 'SECURED').length : 0;
  };

  // Add new vehicle with enhanced duplicate checking
  const handleAddVehicle = async () => {
    if (!newVehicle.vehicle.trim() || !newVehicle.vin.trim()) {
      alert('Please fill in vehicle name and VIN');
      return;
    }

    if (newVehicle.vin.length > 6) {
      alert('VIN must be 6 digits or less');
      return;
    }

    // Check if it's a DO NOT SECURE status
    if (newVehicle.status === 'DO NOT SECURE') {
      const finalReason = doNotSecureReason === 'OTHER' ? customDoNotSecureReason : doNotSecureReason;
      if (!finalReason) {
        alert('Please select or enter a reason for DO NOT SECURE status');
        return;
      }
      newVehicle.doNotSecureReason = finalReason;
    }

    // FIXED: Case-insensitive VIN duplicate check
    if (newVehicle.vin && newVehicle.vin.trim() !== '') {
      const vinToCheck = newVehicle.vin.trim().toUpperCase();
      const existingVehicle = vehicles.find(v =>
        v.vin && v.vin.trim().toUpperCase() === vinToCheck
      );

      if (existingVehicle && existingVehicle.carriedOver && existingVehicle.status !== 'SECURED') {
        const shouldUpdate = window.confirm(
          `A carryover vehicle with VIN "${newVehicle.vin}" already exists.\n\n` +
          `Would you like to update it to SECURED status instead of adding a new entry?\n\n` +
          `Click OK to update the existing carryover vehicle, or Cancel to add as a new vehicle.`
        );

        if (shouldUpdate) {
          try {
            const updatedVehicleData = {
              ...existingVehicle,
              status: 'SECURED',
              securedDate: newVehicle.securedDate || new Date().toISOString().split('T')[0],
              securedFromCarryover: true,
              securedTimestamp: new Date(),
              notes: newVehicle.notes || existingVehicle.notes || '',
              vinVerified: newVehicle.vinVerified,
              images: [...(existingVehicle.images || []), ...newVehicle.images],
              position: newVehicle.position || existingVehicle.position,
              address: newVehicle.address || existingVehicle.address,
              city: newVehicle.city || existingVehicle.city,
              state: newVehicle.state || existingVehicle.state,
              zipCode: newVehicle.zipCode || existingVehicle.zipCode,
              color: newVehicle.color || existingVehicle.color || '',
              driveType: newVehicle.driveType || existingVehicle.driveType || autoDetectDriveType(existingVehicle.vehicle),
              updatedAt: serverTimestamp()
            };

            await setDoc(doc(db, 'users', user.id, 'vehicleWeeks', selectedWeek, 'vehicles', existingVehicle.id), updatedVehicleData);

            if (team && newVehicle.vin) {
              console.log("🔄 Carryover vehicle secured, syncing across team...");
              const userDisplayName = user.displayName || user.email?.split('@')[0] || 'Team Member';

              await markVINAsSecuredAcrossTeam(
                db,
                team.id,
                user.id,
                newVehicle.vin,
                updatedVehicleData.securedDate,
                userDisplayName
              );
            }

            setNewVehicle({
              vehicle: '',
              vin: '',
              vinVerified: false,
              status: 'PENDING PICKUP',
              date: new Date().toISOString().split('T')[0],
              plateNumber: '',
              accountNumber: '',
              financier: '',
              address: '',
              city: '',
              state: '',
              zipCode: '',
              position: null,
              notes: '',
              images: [],
              color: '',
              driveType: '',
              doNotSecureReason: ''
            });

            setShowAddForm(false);

            playMoneySound(soundEnabled);

            alert(`Successfully updated carryover vehicle "${existingVehicle.vehicle}" to SECURED status!`);
            return;

          } catch (error) {
            console.error("Error updating carryover vehicle:", error);
            alert("Error updating carryover vehicle. Please try again.");
            return;
          }
        }
      }
    }

    try {
      setLoading(true);
      const weekId = selectedWeek || getCurrentWeekId();

      await createWeekIfNeeded(db, user.id, weekId);

      // Auto-detect drive type if not manually set
      const detectedDriveType = autoDetectDriveType(newVehicle.vehicle);
      
      // Build full address from components
      const fullAddress = buildFullAddress(
        newVehicle.address,
        newVehicle.city,
        newVehicle.state,
        newVehicle.zipCode
      );
      
      const vehicleData = {
        ...newVehicle,
        vin: newVehicle.vin.toUpperCase().substring(0, 6),
        color: newVehicle.color || '',
        driveType: detectedDriveType,
        fullAddress: fullAddress,  // Store computed full address
        timestamp: serverTimestamp(),
        addedBy: user.displayName || user.email,
        addedByUserId: user.id,
        weekId: weekId,
        createdAt: serverTimestamp()
      };

      // Include position if available
      if (newVehicle.position) {
        vehicleData.position = newVehicle.position;
      }

      const vehiclesRef = collection(db, 'users', user.id, 'vehicleWeeks', weekId, 'vehicles');
      const docRef = await addDoc(vehiclesRef, vehicleData);

      // Add notification to track new vehicle
      const notification = {
        id: Date.now(),
        type: 'team_sync_success',
        message: `🚗 Added new vehicle: ${vehicleData.vehicle} (VIN: ${vehicleData.vin})`,
        timestamp: new Date()
      };

      setTeamSyncNotifications(prev => [notification, ...prev.slice(0, 4)]);
      setTimeout(() => {
        setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 5000);

      // Post to Slack - only post once
      if (team && team.id && !vehicleData.carriedOver) {
        const slackVehicle = formatVehicleForSlack({
          ...vehicleData,
          id: docRef.id,
          teamMemberName: user.displayName || user.email?.split('@')[0] || 'Team Member'
        });
        postVehicleToSlack(team.id, slackVehicle, 'new');
      }

      setNewVehicle({
        vehicle: '',
        vin: '',
        vinVerified: false,
        status: 'PENDING PICKUP',
        date: new Date().toISOString().split('T')[0],
        plateNumber: '',
        accountNumber: '',
        financier: '',
        address: '',
        city: '',
        state: '',
        zipCode: '',
        position: null,
        notes: '',
        images: [],
        color: '',
        driveType: '',
        doNotSecureReason: ''
      });

      // Clear saved draft
      localStorage.removeItem('vehicleFormDraft');

      setShowAddForm(false);
      setShowDoNotSecureReason(false);
      setDoNotSecureReason('');
      setCustomDoNotSecureReason('');
      
      console.log('✅ Vehicle added successfully');

      playNotificationSound(soundEnabled);

    } catch (error) {
      console.error('❌ Error adding vehicle:', error);
      alert('Error adding vehicle. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Update vehicle status
  const handleStatusChange = async (vehicleId, newStatus) => {
    try {
      const weekId = selectedWeek || getCurrentWeekId();
      const vehicleRef = doc(db, 'users', user.id, 'vehicleWeeks', weekId, 'vehicles', vehicleId);

      const originalVehicle = vehicles.find(v => v.id === vehicleId);
      const wasNotSecured = originalVehicle && originalVehicle.status !== 'SECURED';
      const isNowSecured = newStatus === 'SECURED';
      const hasVIN = originalVehicle && originalVehicle.vin && originalVehicle.vin.trim() !== '';

      const updateData = {
        status: newStatus,
        lastUpdated: serverTimestamp()
      };

      if (newStatus === 'SECURED') {
        updateData.securedDate = new Date().toISOString().split('T')[0];
        updateData.securedTimestamp = new Date();
      }

      await updateDoc(vehicleRef, updateData);

      if (wasNotSecured && isNowSecured && hasVIN && team) {
        console.log("🔄 Vehicle just secured with VIN, syncing across team...");

        const userDisplayName = user.displayName || user.email?.split('@')[0] || 'Team Member';

        await markVINAsSecuredAcrossTeam(
          db,
          team.id,
          user.id,
          originalVehicle.vin,
          updateData.securedDate,
          userDisplayName
        );
        
        // Post to Slack when status changes to SECURED
        if (team && team.id) {
          const slackVehicle = formatVehicleForSlack({
            ...originalVehicle,
            status: 'SECURED',
            securedDate: updateData.securedDate,
            securedTimestamp: new Date().toISOString()
          });
          postVehicleToSlack(team.id, slackVehicle, 'secured');
        }
      }

      console.log(`✅ Vehicle status updated to ${newStatus}`);

      if (isNowSecured) {
        playMoneySound(soundEnabled);
      }

    } catch (error) {
      console.error('❌ Error updating vehicle status:', error);
      alert('Error updating status. Please try again.');
    }
  };

  // Delete vehicle
  const handleDeleteVehicle = async (vehicleId) => {
    if (!confirm('Are you sure you want to delete this vehicle? This action cannot be undone.')) return;

    try {
      const weekId = selectedWeek || getCurrentWeekId();
      const vehicleRef = doc(db, 'users', user.id, 'vehicleWeeks', weekId, 'vehicles', vehicleId);
      await deleteDoc(vehicleRef);

      console.log('✅ Vehicle deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting vehicle:', error);
      alert('Error deleting vehicle. Please try again.');
    }
  };

  // Edit handlers
  const handleEditVehicle = (vehicle) => {
    setEditingVehicle(vehicle.id);
    setEditVehicleData({ ...vehicle });
  };

  const handleSaveVehicle = async () => {
    if (!user || !selectedWeek) return;

    try {
      if (editVehicleData.vin && editVehicleData.vin.length > 6) {
        alert('VIN must be 6 digits or less');
        return;
      }

      const originalVehicle = vehicles.find(v => v.id === editingVehicle);
      const wasNotSecured = originalVehicle && originalVehicle.status !== 'SECURED';
      const isNowSecured = editVehicleData.status === 'SECURED';
      const hasVIN = editVehicleData.vin && editVehicleData.vin.trim() !== '';

      // Auto-detect drive type if vehicle name changed
      const detectedDriveType = autoDetectDriveType(editVehicleData.vehicle);
      
      // Build full address from components
      const fullAddress = buildFullAddress(
        editVehicleData.address,
        editVehicleData.city,
        editVehicleData.state,
        editVehicleData.zipCode
      );
      
      const dataToSave = {
        ...editVehicleData,
        vin: editVehicleData.vin ? editVehicleData.vin.toUpperCase().substring(0, 6) : '',
        color: editVehicleData.color || '',
        driveType: detectedDriveType,
        fullAddress: fullAddress,  // Store computed full address
        updatedAt: serverTimestamp()
      };

      await setDoc(doc(db, 'users', user.id, 'vehicleWeeks', selectedWeek, 'vehicles', editingVehicle), dataToSave);

      if (wasNotSecured && isNowSecured && hasVIN && team) {
        console.log("🔄 Vehicle just secured with VIN, syncing across team...");

        const userDisplayName = user.displayName || user.email?.split('@')[0] || 'Team Member';

        await markVINAsSecuredAcrossTeam(
          db,
          team.id,
          user.id,
          dataToSave.vin,
          dataToSave.securedDate,
          userDisplayName
        );
        
        // Post to Slack when edited vehicle is marked as secured
        if (team && team.id) {
          const slackVehicle = formatVehicleForSlack({
            ...dataToSave,
            id: editingVehicle
          });
          postVehicleToSlack(team.id, slackVehicle, 'secured');
        }

        playMoneySound(soundEnabled);
      }

      setEditingVehicle(null);
      setEditVehicleData({});

    } catch (error) {
      console.error("Error saving vehicle data:", error);
    }
  };

  const handleCancelEdit = () => {
    setEditingVehicle(null);
    setEditVehicleData({});
  };

  const handleEditInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    let processedValue = value;

    if (name === 'vin') {
      processedValue = value.substring(0, 6).toUpperCase();
    }

    if (type === 'checkbox') {
      processedValue = checked;
    }

    setEditVehicleData(prev => ({
      ...prev,
      [name]: processedValue
    }));
  };

  const handleNewVehicleChange = (e) => {
    const { name, value, type, checked } = e.target;
    let processedValue = value;

    if (name === 'vin') {
      processedValue = value.substring(0, 6).toUpperCase();
    }

    if (type === 'checkbox') {
      processedValue = checked;
    }

    if (name === 'status') {
      processedValue = value;
      // Show DO NOT SECURE reason selector if that status is selected
      setShowDoNotSecureReason(value === 'DO NOT SECURE');
      if (value !== 'DO NOT SECURE') {
        setDoNotSecureReason('');
        setCustomDoNotSecureReason('');
      }
    }

    setNewVehicle(prev => {
      const updated = {
        ...prev,
        [name]: processedValue
      };
      // Auto-save to localStorage
      localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
      return updated;
    });
  };

  const handleWeekChange = (e) => {
    const newWeekId = e.target.value;
    setSelectedWeek(newWeekId);
    loadVehicleDataForWeek(newWeekId);
  };

  // Scan editing handlers
  const handleEditScans = () => {
    setEditingScans(true);
    setEditScanAmount(vehicleStats.totalScans);
  };

  const handleSaveScans = async () => {
    if (!user || !selectedWeek) return;

    try {
      const scanAmount = parseInt(editScanAmount);

      await updateDoc(doc(db, 'users', user.id, 'vehicleWeeks', selectedWeek), {
        totalScans: scanAmount,
        updatedAt: serverTimestamp()
      });

      const updatedStats = {
        ...vehicleStats,
        totalScans: scanAmount
      };

      setVehicleStats(updatedStats);

      // Recalculate Month and YTD stats
      const stats = await calculateMonthAndYTDStats(db, user, vehicles, selectedWeek, updatedStats);
      setYtdStats(stats);

      setEditingScans(false);
    } catch (error) {
      console.error("Error saving scan amount:", error);
    }
  };

  const handleCancelEditScans = () => {
    setEditingScans(false);
  };

  const handleScanInputChange = (e) => {
    setEditScanAmount(e.target.value);
  };

  // Notes handlers
  const handleEditNotes = (vehicleId, currentNotes) => {
    setEditingNotes(vehicleId);
    setTempNotes(currentNotes || '');
  };

  const handleSaveNotes = async () => {
    if (!user || !selectedWeek || !editingNotes) return;

    try {
      await updateDoc(doc(db, 'users', user.id, 'vehicleWeeks', selectedWeek, 'vehicles', editingNotes), {
        notes: tempNotes,
        updatedAt: serverTimestamp()
      });

      setEditingNotes(null);
      setTempNotes('');
    } catch (error) {
      console.error("Error saving notes:", error);
    }
  };

  const handleCancelNotes = () => {
    setEditingNotes(null);
    setTempNotes('');
  };

  // Toggle actions menu for mobile view
  const toggleActionsMenu = (vehicleId) => {
    setShowActions(prev => ({
      ...prev,
      [vehicleId]: !prev[vehicleId]
    }));
  };

  // SIMPLE & DIRECT PDF Export function - tries autoTable directly
  const exportToPDF = () => {
    console.log('📊 Starting PDF export...');
    
    if (!vehicles || vehicles.length === 0) {
      alert("No vehicle data to export. Please add some vehicles first.");
      return;
    }
    
    console.log('📊 Exporting vehicles to PDF:', vehicles.length, 'vehicles');
    
    // Create loading indicator
    const loadingEl = document.createElement('div');
    loadingEl.style.position = 'fixed';
    loadingEl.style.top = '50%';
    loadingEl.style.left = '50%';
    loadingEl.style.transform = 'translate(-50%, -50%)';
    loadingEl.style.padding = '20px';
    loadingEl.style.background = 'rgba(0, 0, 0, 0.9)';
    loadingEl.style.color = 'white';
    loadingEl.style.borderRadius = '8px';
    loadingEl.style.zIndex = '9999';
    loadingEl.style.textAlign = 'center';
    loadingEl.style.fontFamily = 'Arial, sans-serif';
    loadingEl.innerHTML = `
      <div style="margin-bottom: 15px; font-size: 16px; font-weight: bold;">Generating PDF Report...</div>
      <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db ; border-radius: 50%; margin: 0 auto; animation: spin 1s linear infinite;"></div>
      <div style="margin-top: 10px; font-size: 12px; color: #ccc;">Please wait...</div>
      <style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>
    `;
    document.body.appendChild(loadingEl);

    // Simple library loading function
    const loadLibraries = () => {
      return new Promise((resolve, reject) => {
        // Check if jsPDF already exists
        if (window.jspdf && window.jspdf.jsPDF) {
          console.log('✅ jsPDF already available');
          // Try to load autoTable if not present
          if (!document.querySelector('script[src*="autotable"]')) {
            const autoTableScript = document.createElement('script');
            autoTableScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js';
            autoTableScript.onload = () => {
              console.log('✅ autoTable loaded');
              setTimeout(resolve, 1000); // Give time to attach
            };
            autoTableScript.onerror = () => resolve(); // Continue even if autoTable fails
            document.head.appendChild(autoTableScript);
          } else {
            resolve();
          }
          return;
        }

        // Load jsPDF first
        const jsPdfScript = document.createElement('script');
        jsPdfScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        
        jsPdfScript.onload = () => {
          console.log('✅ jsPDF loaded');
          // Load autoTable
          const autoTableScript = document.createElement('script');
          autoTableScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js';
          
          autoTableScript.onload = () => {
            console.log('✅ autoTable loaded');
            setTimeout(resolve, 1000); // Give time for plugin to attach
          };
          autoTableScript.onerror = () => resolve(); // Continue even if autoTable fails
          document.head.appendChild(autoTableScript);
        };
        
        jsPdfScript.onerror = () => reject(new Error('Failed to load jsPDF'));
        document.head.appendChild(jsPdfScript);
      });
    };

    // Generate PDF with fallback strategy
    const generatePDF = () => {
      try {
        // Get jsPDF
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF({
          orientation: 'landscape',
          unit: 'mm',
          format: 'a4'
        });
        
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        
        // Header Section
        doc.setFillColor(52, 152, 219);
        doc.rect(0, 0, pageWidth, 35, 'F');
        
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(22);
        doc.setFont('helvetica', 'bold');
        doc.text('VEHICLE TRACKING REPORT', pageWidth / 2, 22, { align: 'center' });
        
        // Agent information
        doc.setTextColor(0, 0, 0);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text(`Agent: ${getUserDisplayName()}`, 20, 50);
        doc.text(`Report Date: ${new Date().toLocaleDateString()}`, 20, 58);
        doc.text(`Week: ${getWeekDisplayName()}`, 20, 66);
        
        // Performance Summary
        const weeklyStats = calculateWeeklyStats(vehicles);
        
        doc.setFillColor(240, 248, 255);
        doc.setDrawColor(52, 152, 219);
        doc.setLineWidth(1);
        doc.rect(10, 75, pageWidth - 20, 25, 'FD');
        
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(14);
        doc.setTextColor(52, 152, 219);
        doc.text('PERFORMANCE SUMMARY', 15, 88);
        
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(10);
        doc.setTextColor(0, 0, 0);
        doc.text('WEEK:', 15, 95);
        
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(9);
        doc.text(`Scans: ${vehicleStats.totalScans || 0}`, 55, 95);
        doc.text(`Found: ${weeklyStats.weeklyFound}`, 110, 95);
        doc.text(`Secured: ${weeklyStats.weeklySecured}`, 160, 95);
        doc.text(`Carryover: ${weeklyStats.totalCarryover}`, 210, 95);

        let currentY = 110;

        // Try to use autoTable - with multiple approaches
        const tableHeaders = [
          'Date', 'Vehicle', 'VIN', 'Verified', 'Color', 'Drive', 'Plate #', 'Account #', 
          'Financier', 'Address', 'Status', 'C/O', 'Secured Date', 'Team', 'Bottom Status', 'Notes'
        ];

        const tableData = vehicles.map(vehicle => [
          vehicle.date || '',
          vehicle.vehicle || '',
          vehicle.vin || '',
          vehicle.vinVerified ? 'YES' : 'NO',
          vehicle.color || '',
          vehicle.driveType || '',
          vehicle.plateNumber || '',
          vehicle.accountNumber || '',
          vehicle.financier || '',
          vehicle.fullAddress || buildFullAddress(vehicle.address, vehicle.city, vehicle.state, vehicle.zipCode) || '',
          vehicle.status || '',
          vehicle.carriedOver ? 'YES' : 'NO',
          vehicle.securedDate || '',
          vehicle.autoSecuredFromTeam ? 'TEAM' : 
          (vehicle.fromTeammate ? 'TEAM' : 'SELF'),
          vehicle.bottomStatus || vehicle.doNotSecureReason || '',
          vehicle.notes && vehicle.notes.length > 20 ? 
            vehicle.notes.substring(0, 20) + '...' : vehicle.notes || ''
        ]);

        // Try multiple ways to access autoTable
        let autoTableWorked = false;

        try {
          // Method 1: doc.autoTable (most common)
          if (typeof doc.autoTable === 'function') {
            console.log('📊 Using doc.autoTable method');
            doc.autoTable({
              head: [tableHeaders],
              body: tableData,
              startY: currentY,
              styles: {
                fontSize: 7,
                cellPadding: 2,
                overflow: 'linebreak',
                halign: 'left'
              },
              headStyles: {
                fillColor: [41, 128, 185],
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                fontSize: 8,
                halign: 'center'
              },
              alternateRowStyles: {
                fillColor: [248, 249, 250]
              },
              didParseCell: function(data) {
                // Status column
                if (data.column.index === 10) {
                  const status = data.cell.raw;
                  if (status === 'SECURED') {
                    data.cell.styles.fillColor = [40, 167, 69];
                    data.cell.styles.textColor = [255, 255, 255];
                    data.cell.styles.fontStyle = 'bold';
                  } else if (status === 'FOUND') {
                    data.cell.styles.fillColor = [255, 193, 7];
                    data.cell.styles.textColor = [0, 0, 0];
                    data.cell.styles.fontStyle = 'bold';
                  } else if (status === 'PENDING PICKUP') {
                    data.cell.styles.fillColor = [33, 150, 243];
                    data.cell.styles.textColor = [255, 255, 255];
                    data.cell.styles.fontStyle = 'bold';
                  } else if (status === 'DO NOT SECURE') {
                    data.cell.styles.fillColor = [156, 39, 176];
                    data.cell.styles.textColor = [255, 255, 255];
                    data.cell.styles.fontStyle = 'bold';
                  }
                }
                // VIN Verified column
                if (data.column.index === 3 && data.cell.raw === 'YES') {
                  data.cell.styles.fillColor = [40, 167, 69];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
                // Carryover column
                if (data.column.index === 11 && data.cell.raw === 'YES') {
                  data.cell.styles.fillColor = [255, 152, 0];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
                // Team column
                if (data.column.index === 13 && data.cell.raw === 'TEAM') {
                  data.cell.styles.fillColor = [138, 43, 226];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
                // Bottom Status column
                if (data.column.index === 14 && data.cell.raw && data.cell.raw.length > 0) {
                  data.cell.styles.fillColor = [220, 53, 69];
                  data.cell.styles.textColor = [255, 255, 255];
                  data.cell.styles.fontStyle = 'bold';
                }
              },
              margin: { left: 10, right: 10 }
            });
            autoTableWorked = true;
          }
        } catch (e) {
          console.log('❌ doc.autoTable failed:', e.message);
        }

        // Method 2: window.jspdf.autoTable
        if (!autoTableWorked) {
          try {
            if (window.jspdf && typeof window.jspdf.autoTable === 'function') {
              console.log('📊 Using window.jspdf.autoTable method');
              window.jspdf.autoTable(doc, {
                head: [tableHeaders],
                body: tableData,
                startY: currentY,
                styles: { fontSize: 7, cellPadding: 2 },
                headStyles: { fillColor: [41, 128, 185], textColor: [255, 255, 255], fontStyle: 'bold', fontSize: 8 },
                alternateRowStyles: { fillColor: [248, 249, 250] },
                margin: { left: 10, right: 10 }
              });
              autoTableWorked = true;
            }
          } catch (e) {
            console.log('❌ window.jspdf.autoTable failed:', e.message);
          }
        }

        // Fallback: Create a simple but clean table manually
        if (!autoTableWorked) {
          console.log('📊 Creating manual table as fallback');
          
          doc.setFontSize(12);
          doc.setFont('helvetica', 'bold');
          doc.setTextColor(0, 0, 0);
          doc.text('VEHICLE DATA', 15, currentY);
          currentY += 10;

          // Table header
          doc.setFillColor(41, 128, 185);
          doc.rect(10, currentY, pageWidth - 20, 8, 'F');
          doc.setTextColor(255, 255, 255);
          doc.setFontSize(8);
          doc.setFont('helvetica', 'bold');

          let xPos = 15;
          const colWidths = [20, 30, 15, 15, 15, 10, 20, 20, 25, 40, 20, 12, 20, 15, 25, 30];
          tableHeaders.forEach((header, i) => {
            doc.text(header, xPos, currentY + 5);
            xPos += colWidths[i];
          });

          currentY += 10;
          doc.setTextColor(0, 0, 0);
          doc.setFont('helvetica', 'normal');

          // Table rows
          vehicles.forEach((vehicle, index) => {
            if (currentY > pageHeight - 30) return; // Avoid page overflow

            // Alternate row colors
            if (index % 2 === 0) {
              doc.setFillColor(248, 249, 250);
              doc.rect(10, currentY, pageWidth - 20, 6, 'F');
            }

            xPos = 15;
            const rowData = [
              vehicle.date || '',
              vehicle.vehicle || '',
              vehicle.vin || '',
              vehicle.vinVerified ? 'YES' : 'NO',
              vehicle.color || '',
              vehicle.driveType || '',
              vehicle.plateNumber || '',
              vehicle.accountNumber || '',
              vehicle.financier || '',
              vehicle.fullAddress || buildFullAddress(vehicle.address, vehicle.city, vehicle.state, vehicle.zipCode) || '',
              vehicle.status || '',
              vehicle.carriedOver ? 'YES' : 'NO',
              vehicle.securedDate || '',
              vehicle.autoSecuredFromTeam ? 'TEAM' : (vehicle.fromTeammate ? 'TEAM' : 'SELF'),
              vehicle.bottomStatus || vehicle.doNotSecureReason || '',
              vehicle.notes ? (vehicle.notes.length > 20 ? vehicle.notes.substring(0, 20) + '...' : vehicle.notes) : ''
            ];

            rowData.forEach((data, i) => {
              // Color coding for status
              if (i === 10 && data === 'SECURED') {
                doc.setTextColor(40, 167, 69);
                doc.setFont('helvetica', 'bold');
              } else if (i === 10 && data === 'FOUND') {
                doc.setTextColor(255, 140, 0);
                doc.setFont('helvetica', 'bold');
              } else if (i === 10 && data === 'DO NOT SECURE') {
                doc.setTextColor(156, 39, 176);
                doc.setFont('helvetica', 'bold');
              } else {
                doc.setTextColor(0, 0, 0);
                doc.setFont('helvetica', 'normal');
              }

              doc.text(data.toString(), xPos, currentY + 4);
              xPos += colWidths[i];
            });

            currentY += 6;
          });
        }

        // Footer
        const footerY = pageHeight - 15;
        doc.setDrawColor(200, 200, 200);
        doc.line(10, footerY - 5, pageWidth - 10, footerY - 5);

        doc.setFontSize(8);
        doc.setFont('helvetica', 'normal');
        doc.setTextColor(100, 100, 100);
        doc.text('Generated by NWRepo Analytics System', 15, footerY);
        doc.text('Page 1 of 1 • ' + new Date().toLocaleString(), pageWidth - 15, footerY, { align: 'right' });

        // Save the PDF
        const fileName = `Vehicle_Report_${getUserDisplayName()}_${getWeekDisplayName().replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        console.log('✅ PDF generated successfully');
        playNotificationSound(soundEnabled);

      } catch (error) {
        console.error("❌ Error generating PDF:", error);
        throw error;
      }
    };

    // Start the process
    loadLibraries()
      .then(() => {
        console.log('📚 Libraries ready, generating PDF...');
        generatePDF();
      })
      .catch((error) => {
        console.error("❌ Error:", error);
        alert(`Failed to generate PDF: ${error.message}\n\nPlease try refreshing the page and attempting again.`);
      })
      .finally(() => {
        if (loadingEl && loadingEl.parentNode) {
          loadingEl.parentNode.removeChild(loadingEl);
        }
      });
  };

  // Print functionality
  const handlePrint = () => {
    if (vehicles.length === 0) {
      alert("No vehicle data to export.");
      return;
    }

    setShowPrintView(true);

    setTimeout(() => {
      window.print();
      setTimeout(() => {
        setShowPrintView(false);
      }, 500);
    }, 300);
  };

  // Print view component
  const PrintView = () => {
    const currentDate = new Date().toLocaleDateString();
    const weeklyStats = calculateWeeklyStats(vehicles);

    return (
      <div ref={printRef} className="print-container p-8 bg-white text-black" style={{ display: 'none' }}>
        <style type="text/css" media="print">
          {`
          @page { 
            size: landscape;
            margin: 0.5in;
          }

          body { 
            font-family: Arial, sans-serif;
            color: black;
            background-color: white;
          }

          .print-container {
            display: block !important;
            background-color: white;
            color: black;
          }

          table {
            width: 100%;
            border-collapse: collapse;
          }

          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
          }

          th {
            background-color: #f2f2f2;
            font-weight: bold;
          }

          tr:nth-child(even) {
            background-color: #f9f9f9;
          }
          `}
        </style>

        <div className="print-header">
          <div className="text-2xl font-bold mb-4 text-center">Vehicle Tracking Report</div>
          <div className="mb-2">User: {getUserDisplayName()}</div>
          <div className="mb-2">Week: {vehicleStats.dateRange.start} - {vehicleStats.dateRange.end}</div>
          <div className="mb-2">Total Vehicles: {getProperVehicleCount()}</div>
          <div className="mb-4">Report Generated: {currentDate}</div>
        </div>

        <table>
          <thead>
            <tr>
              <th>Date</th>
              <th>Vehicle</th>
              <th>VIN</th>
              <th>Verified</th>
              <th>Color</th>
              <th>Drive</th>
              <th>Plate #</th>
              <th>Account #</th>
              <th>Financier</th>
              <th>Address</th>
              <th>Status</th>
              <th>C/O</th>
              <th>Secured Date</th>
              <th>Team</th>
              <th>Bottom Status</th>
              <th>Notes</th>
            </tr>
          </thead>
          <tbody>
            {vehicles.length === 0 ? (
              <tr>
                <td colSpan="16" style={{ textAlign: 'center' }}>No vehicles found for this week.</td>
              </tr>
            ) : (
              vehicles.map((vehicle, index) => (
                <tr key={index}>
                  <td>{vehicle.date || ''}</td>
                  <td>{vehicle.vehicle || ''}</td>
                  <td>{vehicle.vin || ''}</td>
                  <td>{vehicle.vinVerified ? 'YES' : 'NO'}</td>
                  <td>{vehicle.color || ''}</td>
                  <td>{vehicle.driveType || ''}</td>
                  <td>{vehicle.plateNumber || ''}</td>
                  <td>{vehicle.accountNumber || ''}</td>
                  <td>{vehicle.financier || ''}</td>
                  <td>{vehicle.fullAddress || buildFullAddress(vehicle.address, vehicle.city, vehicle.state, vehicle.zipCode) || ''}</td>
                  <td>{vehicle.status || ''}</td>
                  <td>{vehicle.carriedOver ? 'YES' : 'NO'}</td>
                  <td>{vehicle.securedDate || ''}</td>
                  <td>{vehicle.autoSecuredFromTeam ? 'TEAM' : (vehicle.fromTeammate ? 'TEAM' : 'SELF')}</td>
                  <td>{vehicle.bottomStatus || vehicle.doNotSecureReason || ''}</td>
                  <td>{vehicle.notes || ''}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    );
  };

  // Sound toggle component
  const SoundToggle = () => (
    <div className="flex items-center">
      <button
        onClick={() => setSoundEnabled(!soundEnabled)}
        className={`flex items-center ${soundEnabled ? 'text-blue-400' : 'text-gray-500'}`}
        title={soundEnabled ? "Disable notification sounds" : "Enable notification sounds"}
      >
        <span className="mr-1 text-lg">
          {soundEnabled ? '🔊' : '🔇'}
        </span>
        <span className="text-xs hidden sm:inline">
          {soundEnabled ? 'Sound On' : 'Sound Off'}
        </span>
      </button>
    </div>
  );

  // Initialize Firebase and load data
  useEffect(() => {
    let isMounted = true;

    const initializeApp = async () => {
      try {
        console.log('🚀 Starting standalone vehicle tracker initialization...');

        const targetUserId = getUserIdFromUrl();

        if (!targetUserId) {
          const currentUrl = window.location.href;
          const errorMessage = 
            `No user ID provided in URL.\n\n` +
            `Current URL: ${currentUrl}\n\n` +
            `Expected formats:\n` +
            `• ${window.location.origin}/vehicles?user=YOUR_USER_ID\n` +
            `• ${window.location.origin}/vehicles/YOUR_USER_ID\n\n` +
            `Contact your administrator for your USER_ID.`;

          console.error('❌ No user ID found:', errorMessage);

          if (isMounted) {
            setInitError(errorMessage);
            setLoading(false);
          }
          return;
        }

        const { firestore, storageInstance } = await initializeFirebase(targetUserId);

        if (isMounted) {
          setDb(firestore);
          setStorage(storageInstance);
        }

        const { userInfo, profileData } = await loadUserData(firestore, targetUserId);

        if (isMounted) {
          setUser(userInfo);
          setUserProfile(profileData);
          
          const canSecure = checkUserPermissions(userInfo, profileData);
          setCanSecureVehicles(canSecure);
          console.log('🔐 User permissions checked - Can secure vehicles:', canSecure);
        }

        const userTeam = await findUserTeam(firestore, targetUserId);

        if (isMounted) {
          setTeam(userTeam);
          setLoading(false);
        }

      } catch (err) {
        console.error("❌ Error initializing:", err);
        if (isMounted) {
          setInitError(err.message || "Failed to load user data");
          setLoading(false);
        }
      }
    };

    initializeApp();

    return () => {
      isMounted = false;
    };
  }, []);

  // Load data when user is ready
  useEffect(() => {
    if (user && db) {
      console.log('🚗 Loading vehicle data...');
      loadAvailableWeeks();
      createWeekIfNeeded(db, user.id, getCurrentWeekId());
    }
  }, [user, db]);

  // NEW: Load orders when team is available
  useEffect(() => {
    if (db && team) {
      console.log('📋 Loading orders for team...');
      const unsubscribe = loadOrders();
      return () => {
        if (typeof unsubscribe === 'function') {
          unsubscribe();
        }
      };
    }
  }, [db, team, loadOrders]);

  // Check for team-secured vehicles
  useEffect(() => {
    if (vehicles && vehicles.length > 0) {
      vehicles.forEach(vehicle => {
        if (vehicle.autoSecuredFromTeam && vehicle.securedByTeammate && !vehicle.notificationShown) {
          showTeamSyncNotification(vehicle);
          vehicle.notificationShown = true;
        }
      });
    }
  }, [vehicles]);

  // Recalculate monthly and YTD stats when vehicles change
  useEffect(() => {
    if (user && db && vehicles.length > 0) {
      calculateMonthAndYTDStats(db, user, vehicles, selectedWeek, vehicleStats).then(stats => {
        setYtdStats(stats);
      });
    }
  }, [vehicles]);

  // Detect viewport size for responsive layout
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    handleResize(); // Call immediately to set initial state

    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update permissions when user profile changes
  useEffect(() => {
    if (user && userProfile) {
      const canSecure = checkUserPermissions(user, userProfile);
      setCanSecureVehicles(canSecure);
      console.log('🔐 Permissions updated - Can secure vehicles:', canSecure);
    }
  }, [user, userProfile]);

  // Error boundary for Firebase/App errors
  if (initError) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center p-4">
        <div className="text-center max-w-lg mx-auto">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold mb-4">Error Loading Vehicle Tracker</h1>
          <div className="text-gray-300 mb-6 bg-gray-800 p-4 rounded-lg text-left text-sm max-h-64 overflow-y-auto">
            <pre className="whitespace-pre-wrap font-mono text-xs">{initError}</pre>
          </div>
          <div className="space-y-2">
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded font-medium mr-3"
            >
              Retry
            </button>
            <button
              onClick={() => window.history.back()}
              className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded font-medium"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-300 text-lg">Loading vehicle tracker...</p>
          <p className="text-gray-500 text-sm mt-2">Setting up Firebase connection...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-yellow-500 text-6xl mb-4">🔍</div>
          <h1 className="text-2xl font-bold mb-2">User Not Found</h1>
          <p className="text-gray-300">No user found with the provided ID</p>
        </div>
      </div>
    );
  }

  // Get weekly stats for display
  const weeklyStats = calculateWeeklyStats(vehicles);

// Mobile-friendly input styles
const mobileInputStyles = isMobile 
? "w-full bg-gray-900 border-2 border-gray-600 rounded-lg px-4 py-3 text-white text-base focus:border-blue-500 focus:outline-none"
: "w-full bg-gray-900 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none";

const mobileLabelStyles = isMobile
? "block text-gray-300 text-sm mb-2 font-semibold"
: "block text-gray-300 text-xs mb-1 font-semibold";

// Main render
return (
<>
  <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white">
    {/* Header */}
    <div className="bg-black bg-opacity-50 backdrop-blur-lg shadow-xl border-b border-gray-700">
      <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="text-3xl mr-3 animate-pulse">{user.avatar}</div>
            <div>
              <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                Vehicle Tracker
              </h1>
              <p className="text-xs sm:text-sm text-gray-300">
                {user.displayName} • {team ? team.name : 'No Team'} • {user.jobTitle}
                {canSecureVehicles && (
                  <span className="ml-2 bg-green-600 text-white text-xs px-2 py-1 rounded-full">
                    🚚 Can Secure
                  </span>
                )}
              </p>
            </div>
          </div>

          {/* Center - Team Tracker Toggle Button */}
          <div className="flex-1 flex justify-center">
            <button
              onClick={() => setShowTeamTracker(!showTeamTracker)}
              className={`${isMobile ? 'px-3 py-2 text-sm min-w-[44px] min-h-[44px]' : 'px-6 py-2 text-base'} rounded-lg font-semibold transition-all duration-200 flex items-center gap-2 touch-manipulation ${
                showTeamTracker
                  ? 'bg-purple-600 hover:bg-purple-700 active:bg-purple-800 text-white shadow-lg'
                  : 'bg-gray-700 hover:bg-gray-600 active:bg-gray-500 text-gray-300 border border-gray-600'
              }`}
              style={{ WebkitTapHighlightColor: 'transparent' }}
            >
              <span className="text-lg">👥</span>
              <span className={isMobile ? 'hidden sm:block' : 'block'}>Team Tracker</span>
              <span className={`transition-transform duration-200 ${showTeamTracker ? 'rotate-180' : ''}`}>
                ▼
              </span>
            </button>
          </div>

          <div className="text-right hidden sm:block">
            <div className="text-xs text-gray-400">Enhanced Mobile App</div>
            <div className="text-sm text-blue-300 font-semibold">recoveriqs.net</div>
            <div className="text-xs text-gray-500">
              {vehicles.length > 0 && `Last sync: ${new Date().toLocaleTimeString()}`}
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Main Content */}
    <div className="px-4 py-4 max-w-7xl mx-auto">
      {/* Team Sync Notifications */}
      {teamSyncNotifications.length > 0 && (
        <div className="mb-4">
          <div className="space-y-2">
            {teamSyncNotifications.map(notification => (
              <div 
                key={notification.id} 
                className={`bg-opacity-90 backdrop-blur-lg border rounded-xl p-3 text-sm shadow-lg ${getNotificationColor(notification.type)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="font-semibold">{notification.message}</span>
                  </div>
                  <button 
                    onClick={() => setTeamSyncNotifications(prev => prev.filter(n => n.id !== notification.id))}
                    className="text-current hover:opacity-75 ml-2 text-lg"
                    title="Dismiss notification"
                  >
                    ✕
                  </button>
                </div>
                <div className="text-xs opacity-75 mt-1">
                  {notification.timestamp.toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Enhanced Stats Cards using imported component */}
      <StatsCards 
        vehicleStats={vehicleStats}
        weeklyStats={weeklyStats}
        editingScans={editingScans}
        editScanAmount={editScanAmount}
        handleEditScans={handleEditScans}
        handleSaveScans={handleSaveScans}
        handleCancelEditScans={handleCancelEditScans}
        handleScanInputChange={handleScanInputChange}
      />

      {/* Monthly and YTD Stats using imported component */}
      <MonthlyAndYTDStats ytdStats={ytdStats} />

      {/* Vehicle Map Display - ENHANCED: Now includes orders prop */}
      <VehicleMapDisplay
        vehicles={vehicles}
        teamVehicles={teamVehicles}
        orders={orders}
        db={db}
        user={user}
        userProfile={userProfile}
        team={team}
        selectedWeek={selectedWeek}
        onVehicleDeleted={handleVehicleDeletedFromMap}
        selectedVehicleId={selectedVehicleId}
        onVehicleSelect={handleVehicleSelect}
        selectedOrderId={selectedOrderId}
        onOrderSelect={handleOrderSelect}
        onOrderNavigate={handleOrderNavigate}
        onOrderSecure={handleOrderSecure}
        onOrderCardDisplay={handleOrderCardDisplay}
      />

      {/* Team Vehicles Component - Conditionally rendered with toggle */}
      {showTeamTracker && (
        <TeamVehicleTracker
          db={db}
          user={user}
          team={team}
          selectedWeek={selectedWeek}
          vehicles={vehicles}
          setVehicles={setVehicles}
          canSecureVehicles={canSecureVehicles}
          soundEnabled={soundEnabled}
          updateVehicleWeekStats={updateVehicleWeekStats}
          showImageInModal={showImageInModal}
          teamSyncNotifications={teamSyncNotifications}
          setTeamSyncNotifications={setTeamSyncNotifications}
          setTeamVehicles={setTeamVehicles}
          selectedVehicleId={selectedVehicleId}
          onVehicleSelect={handleVehicleSelect}
        />
      )}

      {/* Vehicle Management Section */}
      <div className="bg-gray-900 bg-opacity-90 backdrop-blur-lg rounded-xl shadow-xl border border-gray-700 p-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <div className="mb-3 md:mb-0">
            <h3 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-1">
              Weekly Vehicles List
            </h3>
            <div className="flex flex-wrap items-center text-gray-300 text-xs gap-2">
            <span className="bg-gray-800 px-2 py-1 rounded">{getUserDisplayName()}</span>
              <span className="bg-gray-800 px-2 py-1 rounded">
                {vehicleStats.dateRange.start || ''} - {vehicleStats.dateRange.end || ''}
              </span>
              <span className="bg-green-800 px-2 py-1 rounded">
                SECURED: {getProperVehicleCount()}
              </span>
              <span className="bg-purple-800 px-2 py-1 rounded">
                CARRYOVER: {weeklyStats.totalCarryover}
              </span>
              {/* NEW: Orders count display */}
              {orders.length > 0 && (
                <span className="bg-blue-800 px-2 py-1 rounded">
                  ORDERS: {orders.length}
                </span>
              )}
              {ordersLoading && (
                <span className="bg-yellow-800 px-2 py-1 rounded animate-pulse">
                  Loading orders...
                </span>
              )}
            </div>
          </div>

          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
            <select
              value={selectedWeek || ''}
              onChange={handleWeekChange}
              className="bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm shadow-lg"
            >
              {availableWeeks.length === 0 ? (
                <option value="">No weeks available</option>
              ) : (
                availableWeeks.map(week => (
                  <option key={week.id} value={week.id}>
                    {week.displayRange || 'Week of ' + new Date(week.startDate).toLocaleDateString()}
                    {week.startDate <= new Date() && week.endDate >= new Date() ? ' (Current)' : ''}
                  </option>
                ))
              )}
            </select>

            <button
              onClick={checkForMissingCarryovers}
              disabled={loading || !selectedWeek}
              className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 disabled:from-gray-600 disabled:to-gray-700 text-white px-3 py-2 rounded-lg shadow-lg flex items-center justify-center font-semibold transition-all disabled:cursor-not-allowed"
              title="Check for vehicles from previous week that should be carried over"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span className="hidden sm:inline">Check Carryovers</span>
              <span className="sm:hidden">Check</span>
            </button>

            <button
              onClick={cleanupIncorrectlyAddedVehicles}
              className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-3 py-2 rounded-lg shadow-lg flex items-center justify-center font-semibold transition-all"
              title="Remove all team vehicles that were incorrectly added to your list"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              <span className="hidden sm:inline">Deep Clean</span>
              <span className="sm:hidden">Clean</span>
            </button>

            <button
              onClick={() => setShowAddForm(true)}
              className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-4 py-2 rounded-lg shadow-lg flex items-center justify-center font-semibold transition-all transform hover:scale-105"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Add Vehicle
            </button>

            <button
              onClick={exportToPDF}
              disabled={vehicles.length === 0}
              className={`${
                vehicles.length === 0 
                  ? 'bg-gray-600 cursor-not-allowed' 
                  : 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800'
              } text-white px-4 py-2 rounded-lg shadow-lg flex items-center justify-center font-semibold transition-all`}
              title="Export to PDF"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              PDF
            </button>
          </div>
        </div>

        {/* Add Vehicle Form - ENHANCED WITH MOBILE-FRIENDLY LAYOUT AND NEW TOGGLE/STATUS */}
        {showAddForm && (
          <div className="bg-gray-800 bg-opacity-90 p-4 rounded-xl mb-4 border border-gray-600 shadow-xl">
            <div className="flex justify-between items-center mb-3">
              <h4 className="text-lg font-bold text-blue-300">Add New Vehicle</h4>
              {newVehicle.vehicle || newVehicle.vin || newVehicle.address ? (
                <span className="text-xs text-green-400 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Draft saved
                </span>
              ) : null}
            </div>

            {/* ENHANCED: Get Location Button - MORE PROMINENT */}
            <div className="mb-4 bg-blue-900 bg-opacity-50 p-4 rounded-lg border-2 border-blue-500">
              <button
                onClick={getCurrentLocationAndAddress}
                disabled={isGettingLocation}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-4 rounded-lg font-bold shadow-lg flex items-center justify-center transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed text-lg"
              >
                {isGettingLocation ? (
                  <>
                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-white mr-3"></div>
                    Getting Your Location...
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    📍 Get Current Location & Address
                  </>
                )}
              </button>

              {/* Location status and coordinates display */}
              {newVehicle.position && (
                <div className="mt-3 bg-green-900 bg-opacity-50 p-3 rounded-lg">
                  <div className="text-green-400 font-bold mb-1">✅ Location Captured Successfully!</div>
                  <div className="text-sm text-green-300">
                    <div className="font-mono">Latitude: {newVehicle.position.lat.toFixed(6)}</div>
                    <div className="font-mono">Longitude: {newVehicle.position.lng.toFixed(6)}</div>
                  </div>
                </div>
              )}

              {locationError && (
                <div className="mt-3 bg-red-900 bg-opacity-50 p-3 rounded-lg">
                  <div className="text-red-400 text-sm">{locationError}</div>
                </div>
              )}

              <div className="mt-2 text-xs text-gray-300">
                📱 This captures your precise GPS location and auto-fills the address for tow truck drivers
              </div>
            </div>

            {/* Mobile-optimized form fields */}
            <div className={`grid ${isMobile ? 'grid-cols-1 gap-4' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3'} mb-3`}>
              <div>
                <label className={mobileLabelStyles}>Date</label>
                <input
                  type="date"
                  name="date"
                  value={newVehicle.date}
                  onChange={handleNewVehicleChange}
                  className={mobileInputStyles}
                />
              </div>
              <div className={isMobile ? '' : 'md:col-span-2'}>
                <label className={mobileLabelStyles}>Vehicle * (Year Make Model)</label>
                <input
                  type="text"
                  name="vehicle"
                  value={newVehicle.vehicle}
                  onChange={handleNewVehicleChange}
                  placeholder="2020 Honda Civic"
                  className={mobileInputStyles}
                />
                {/* Drive Type Info */}
                {newVehicle.vehicle && (
                  <div className="mt-1 text-xs text-gray-400">
                    Auto-detected: <span className="text-yellow-400 font-bold">{autoDetectDriveType(newVehicle.vehicle)}</span>
                    {(() => {
                      const info = getDriveTypeInfo(newVehicle.vehicle);
                      return info.subModels.length > 0 ? (
                        <span className="ml-2">({info.subModels.join(', ')})</span>
                      ) : null;
                    })()}
                  </div>
                )}
              </div>
              <div>
                <label className={mobileLabelStyles}>VIN * (Last 6 digits)</label>
                <input
                  type="text"
                  name="vin"
                  value={newVehicle.vin}
                  onChange={handleNewVehicleChange}
                  maxLength={6}
                  placeholder="123456"
                  className={`${mobileInputStyles} font-mono`}
                />
              </div>
              
              {/* NEW VIN VERIFIED TOGGLE BUTTON */}
              <div>
                <label className={mobileLabelStyles}>VIN Verified</label>
                <button
                  type="button"
                  onClick={() => {
                    setNewVehicle(prev => {
                      const updated = {
                        ...prev,
                        vinVerified: !prev.vinVerified
                      };
                      localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
                      return updated;
                    });
                  }}
                  className={`w-full mt-2 relative inline-flex items-center justify-center px-4 py-3 rounded-lg font-semibold transition-all ${
                    newVehicle.vinVerified 
                      ? 'bg-green-600 hover:bg-green-700 text-white' 
                      : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                  }`}
                >
                  <span className="mr-2">{newVehicle.vinVerified ? '✓' : '○'}</span>
                  {newVehicle.vinVerified ? 'VIN Verified' : 'VIN Not Verified'}
                </button>
              </div>
              
              <div>
                <label className={mobileLabelStyles}>Color</label>
                <input
                  type="text"
                  name="color"
                  value={newVehicle.color}
                  onChange={handleNewVehicleChange}
                  list="vehicle-colors"
                  placeholder="Black"
                  className={mobileInputStyles}
                />
                <datalist id="vehicle-colors">
                  {VEHICLE_COLORS.map(color => (
                    <option key={color} value={color} />
                  ))}
                </datalist>
              </div>
              
              {/* NEW STATUS SELECTOR - DO NOT SECURE BUTTON */}
              <div>
                <label className={mobileLabelStyles}>Status</label>
                <div className="mt-2">
                  {/* Default status display */}
                  <div className="bg-blue-900 bg-opacity-30 px-3 py-2 rounded-lg text-blue-300 text-sm font-semibold mb-2">
                    Default: PENDING PICKUP
                  </div>
                  
                  {/* Do Not Secure toggle button */}
                  <button
                    type="button"
                    onClick={() => {
                      const isDoNotSecure = newVehicle.status === 'DO NOT SECURE';
                      setNewVehicle(prev => {
                        const updated = {
                          ...prev,
                          status: isDoNotSecure ? 'PENDING PICKUP' : 'DO NOT SECURE'
                        };
                        localStorage.setItem('vehicleFormDraft', JSON.stringify(updated));
                        return updated;
                      });
                      setShowDoNotSecureReason(!isDoNotSecure);
                      if (isDoNotSecure) {
                        setDoNotSecureReason('');
                        setCustomDoNotSecureReason('');
                      }
                    }}
                    className={`w-full px-4 py-3 rounded-lg font-semibold transition-all ${
                      newVehicle.status === 'DO NOT SECURE'
                        ? 'bg-purple-600 hover:bg-purple-700 text-white' 
                        : 'bg-gray-700 hover:bg-gray-600 text-gray-300 border-2 border-dashed border-gray-500'
                    }`}
                  >
                    <span className="mr-2">{newVehicle.status === 'DO NOT SECURE' ? '🚫' : '⚪'}</span>
                    {newVehicle.status === 'DO NOT SECURE' ? 'DO NOT SECURE (Selected)' : 'Mark as DO NOT SECURE'}
                  </button>
                </div>
              </div>

              {/* DO NOT SECURE Reason Selector */}
              {showDoNotSecureReason && (
                <div className={isMobile ? 'col-span-1' : 'md:col-span-2'}>
                  <label className={mobileLabelStyles}>DO NOT SECURE Reason *</label>
                  <select
                    value={doNotSecureReason}
                    onChange={(e) => setDoNotSecureReason(e.target.value)}
                    className={mobileInputStyles}
                  >
                    <option value="">Select a reason...</option>
                    {DO_NOT_SECURE_REASONS.map(reason => (
                      <option key={reason} value={reason}>{reason}</option>
                    ))}
                  </select>
                  
                  {doNotSecureReason === 'OTHER' && (
                    <input
                      type="text"
                      value={customDoNotSecureReason}
                      onChange={(e) => setCustomDoNotSecureReason(e.target.value)}
                      placeholder="Enter custom reason..."
                      className={`${mobileInputStyles} mt-2`}
                    />
                  )}
                </div>
              )}

              <div>
                <label className={mobileLabelStyles}>Plate Number</label>
                <input
                  type="text"
                  name="plateNumber"
                  value={newVehicle.plateNumber}
                  onChange={handleNewVehicleChange}
                  placeholder="ABC123"
                  className={mobileInputStyles}
                />
              </div>
              <div>
                <label className={mobileLabelStyles}>Account Number</label>
                <input
                  type="text"
                  name="accountNumber"
                  value={newVehicle.accountNumber}
                  onChange={handleNewVehicleChange}
                  placeholder="********"
                  className={mobileInputStyles}
                />
              </div>
              <div className={isMobile ? '' : 'md:col-span-2'}>
                <label className={mobileLabelStyles}>Financier</label>
                <input
                  type="text"
                  name="financier"
                  value={newVehicle.financier}
                  onChange={handleNewVehicleChange}
                  placeholder="Bank Name"
                  className={mobileInputStyles}
                />
              </div>

              {/* Address fields - Enhanced with city, state, zip */}
              <div className={isMobile ? 'col-span-1' : 'md:col-span-4'}>
                <label className={mobileLabelStyles}>
                  Street Address {newVehicle.position && <span className="text-green-400">(Auto-filled from GPS)</span>}
                </label>
                <input
                  type="text"
                  name="address"
                  value={newVehicle.address}
                  onChange={handleNewVehicleChange}
                  placeholder="123 Main St"
                  className={mobileInputStyles}
                />
              </div>

              {/* City field */}
              <div className={isMobile ? 'col-span-1' : 'md:col-span-1'}>
                <label className={mobileLabelStyles}>
                  City * {newVehicle.position && <span className="text-green-400">(Auto-filled)</span>}
                </label>
                <input
                  type="text"
                  name="city"
                  value={newVehicle.city}
                  onChange={handleNewVehicleChange}
                  placeholder="Chicago"
                  className={mobileInputStyles}
                  required
                />
              </div>

              {/* State field */}
              <div className={isMobile ? 'col-span-1' : 'md:col-span-1'}>
                <label className={mobileLabelStyles}>
                  State * {newVehicle.position && <span className="text-green-400">(Auto-filled)</span>}
                </label>
                <select
                  name="state"
                  value={newVehicle.state}
                  onChange={handleNewVehicleChange}
                  className={mobileInputStyles}
                  required
                >
                  <option value="">Select State</option>
                  {STATE_OPTIONS.map(state => (
                    <option key={state.value} value={state.value}>
                      {state.label} ({state.value})
                    </option>
                  ))}
                </select>
              </div>

              {/* Zip Code field */}
              <div className={isMobile ? 'col-span-1' : 'md:col-span-1'}>
                <label className={mobileLabelStyles}>
                  ZIP Code {newVehicle.position && <span className="text-green-400">(Auto-filled)</span>}
                </label>
                <input
                  type="text"
                  name="zipCode"
                  value={newVehicle.zipCode}
                  onChange={handleNewVehicleChange}
                  placeholder="60601"
                  maxLength={10}
                  className={mobileInputStyles}
                />
              </div>

              {/* Full address preview */}
              {(newVehicle.address || newVehicle.city || newVehicle.state) && (
                <div className={isMobile ? 'col-span-1' : 'md:col-span-4'}>
                  <div className="bg-blue-900 bg-opacity-30 rounded-lg p-3 border border-blue-600">
                    <p className="text-blue-300 text-xs font-semibold mb-1">📍 Full Address Preview:</p>
                    <p className="text-white font-semibold text-sm">
                      {buildFullAddress(newVehicle.address, newVehicle.city, newVehicle.state, newVehicle.zipCode)}
                    </p>
                  </div>
                </div>
              )}

              {/* Enhanced Photo Upload for Mobile - Moved below address */}
              <div className={isMobile ? 'col-span-1' : 'md:col-span-4'}>
                <label className={mobileLabelStyles}>Photos</label>
                
                {/* Hidden file inputs */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleFileSelect}
                  className="hidden"
                />
                
                <input
                  ref={cameraInputRef}
                  type="file"
                  accept="image/*"
                  capture="environment"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                
                {/* Mobile-friendly button group */}
                <div className="flex gap-2">
                  <button
                    onClick={() => cameraInputRef.current?.click()}
                    disabled={uploadingImages}
                    className={`flex-1 ${mobileInputStyles} hover:bg-gray-800 transition-colors flex items-center justify-center`}
                  >
                    {uploadingImages ? `Uploading... ${Math.round(imageUploadProgress)}%` : '📷 Camera'}
                  </button>
                  
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploadingImages}
                    className={`flex-1 ${mobileInputStyles} hover:bg-gray-800 transition-colors flex items-center justify-center`}
                  >
                    📁 Gallery
                  </button>
                </div>
                
                {newVehicle.images.length > 0 && (
                  <div className="mt-2 text-xs text-gray-400">
                    {newVehicle.images.length} photo(s) added
                  </div>
                )}
              </div>

              {/* Notes field - full width */}
              <div className={isMobile ? 'col-span-1' : 'md:col-span-4'}>
                <label className={mobileLabelStyles}>Notes</label>
                <textarea
                  name="notes"
                  value={newVehicle.notes}
                  onChange={handleNewVehicleChange}
                  placeholder="Gate code, special instructions, etc..."
                  rows="3"
                  className={mobileInputStyles}
                />
              </div>
            </div>

            {/* Note about status */}
            <div className="mb-3 p-3 bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg">
              <p className="text-blue-300 text-sm">
                ℹ️ All orders default to "PENDING PICKUP" status unless you select "DO NOT SECURE". Drive type is automatically detected from the vehicle make/model.
              </p>
              <p className="text-blue-300 text-xs mt-1">
                💾 Your form data is automatically saved and will be restored if you leave and come back.
              </p>
            </div>

            {/* Image Preview - Mobile Optimized */}
            {newVehicle.images.length > 0 && (
              <div className="mb-3">
                <p className={mobileLabelStyles}>Photo Preview</p>
                <div className={`grid ${isMobile ? 'grid-cols-3' : 'grid-cols-4 md:grid-cols-6'} gap-2`}>
                  {newVehicle.images.map((image, idx) => (
                    <div key={idx} className="relative aspect-square rounded-lg overflow-hidden border border-gray-600">
                      <img 
                        src={image.url} 
                        alt={`Preview ${idx + 1}`}
                        className="w-full h-full object-cover"
                      />
                      <button
                        onClick={() => removeImageFromNewVehicle(idx)}
                        className="absolute top-1 right-1 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-700"
                      >
                        ✕
                      </button>
                      {image.location && (
                        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1">
                          📍 GPS
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Action buttons - Mobile Optimized */}
            <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'justify-end space-x-2'}`}>
              {isMobile && (
                <button
                  onClick={handleAddVehicle}
                  disabled={loading || !newVehicle.vehicle.trim() || !newVehicle.vin.trim() || uploadingImages}
                  className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-4 rounded-lg text-lg font-bold shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? '⏳ Adding Vehicle...' : '✅ Add Vehicle'}
                </button>
              )}

              <button
                onClick={() => {
                  setShowAddForm(false);
                  setShowDoNotSecureReason(false);
                  setDoNotSecureReason('');
                  setCustomDoNotSecureReason('');
                  // Clear form and localStorage
                  localStorage.removeItem('vehicleFormDraft');
                  setNewVehicle({
                    vehicle: '',
                    vin: '',
                    vinVerified: false,
                    status: 'PENDING PICKUP',
                    date: new Date().toISOString().split('T')[0],
                    plateNumber: '',
                    accountNumber: '',
                    financier: '',
                    address: '',
                    city: '',
                    state: '',
                    zipCode: '',
                    position: null,
                    notes: '',
                    images: [],
                    color: '',
                    driveType: '',
                    doNotSecureReason: ''
                  });
                }}
                className={`bg-gray-600 hover:bg-gray-700 text-white ${isMobile ? 'px-6 py-3' : 'px-4 py-2'} rounded-lg text-sm font-semibold shadow-lg`}
              >
                Cancel
              </button>

              {!isMobile && (
                <button
                  onClick={handleAddVehicle}
                  disabled={loading || !newVehicle.vehicle.trim() || !newVehicle.vin.trim() || uploadingImages}
                  className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-4 py-2 rounded-lg text-sm font-semibold shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Adding...' : 'Add Vehicle'}
                </button>
              )}
            </div>
          </div>
        )}

        {/* Vehicle List - Enhanced Mobile Card View */}
        {selectedWeek === null ? (
          <div className="bg-gray-800 bg-opacity-50 p-8 rounded-xl text-center">
            <p className="text-gray-400">No week selected</p>
          </div>
        ) : vehicles.length === 0 ? (
          <div className="bg-gray-800 bg-opacity-50 p-8 rounded-xl text-center">
            <span className="text-5xl mb-3 block">🚗</span>
            <p className="text-gray-300 font-semibold">No vehicles found for this week</p>
            <p className="text-gray-400 text-sm mt-1">Click "Add Vehicle" to start tracking</p>
          </div>
        ) : (
          <div className="space-y-3">
            {vehicles.map((vehicle) => {
              const isTeamSecured = vehicle.autoSecuredFromTeam && vehicle.securedByTeammate;

              if (editingVehicle === vehicle.id) {
                // Editing form for mobile - WITH ENHANCED MOBILE LAYOUT AND VIN TOGGLE
                return (
                  <div key={vehicle.id} className="bg-gray-800 rounded-xl overflow-hidden border-2 border-blue-500 shadow-xl">
                    <div className="bg-blue-600 p-3">
                      <h3 className="text-xl font-bold text-white">Editing Vehicle</h3>
                    </div>
                    <div className="p-4">
                      {/* Get Location Button for Edit */}
                      <div className="mb-3">
                        <button
                          onClick={getCurrentLocationAndAddressForEdit}
                          disabled={isGettingEditLocation}
                          className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-3 rounded-lg font-bold shadow-lg flex items-center justify-center transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isGettingEditLocation ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                              Getting Location...
                            </>
                          ) : (
                            <>
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                              </svg>
                              Update Location & Address
                            </>
                          )}
                        </button>
                        {editVehicleData.position && (
                          <div className="mt-1 text-xs text-green-400">
                            ✓ Location: {editVehicleData.position.lat.toFixed(6)}, {editVehicleData.position.lng.toFixed(6)}
                          </div>
                        )}
                      </div>

                      <div className={`grid ${isMobile ? 'grid-cols-1 gap-3' : 'grid-cols-2 gap-3'} mb-3`}>
                        <div>
                          <label className={mobileLabelStyles}>Date</label>
                          <input
                            type="date"
                            name="date"
                            value={editVehicleData.date || ''}
                            onChange={handleEditInputChange}
                            className={mobileInputStyles}
                          />
                        </div>
                        <div className={isMobile ? '' : 'col-span-1'}>
                          <label className={mobileLabelStyles}>Vehicle</label>
                          <input
                            type="text"
                            name="vehicle"
                            value={editVehicleData.vehicle || ''}
                            onChange={handleEditInputChange}
                            className={mobileInputStyles}
                          />
                          {/* Drive Type Info for Edit */}
                          {editVehicleData.vehicle && (
                            <div className="mt-1 text-xs text-gray-400">
                              Auto-detected: <span className="text-yellow-400 font-bold">{autoDetectDriveType(editVehicleData.vehicle)}</span>
                            </div>
                          )}
                        </div>
                        <div>
                          <label className={mobileLabelStyles}>VIN (6 max)</label>
                          <input
                            type="text"
                            name="vin"
                            value={editVehicleData.vin || ''}
                            onChange={handleEditInputChange}
                            maxLength={6}
                            className={`${mobileInputStyles} font-mono`}
                          />
                        </div>
                        
                        {/* VIN VERIFIED TOGGLE BUTTON FOR EDIT */}
                        <div>
                          <label className={mobileLabelStyles}>VIN Verified</label>
                          <button
                            type="button"
                            onClick={() => {
                              setEditVehicleData(prev => ({
                                ...prev,
                                vinVerified: !prev.vinVerified
                              }));
                            }}
                            className={`w-full mt-2 relative inline-flex items-center justify-center px-4 py-3 rounded-lg font-semibold transition-all ${
                              editVehicleData.vinVerified 
                                ? 'bg-green-600 hover:bg-green-700 text-white' 
                                : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                            }`}
                          >
                            <span className="mr-2">{editVehicleData.vinVerified ? '✓' : '○'}</span>
                            {editVehicleData.vinVerified ? 'VIN Verified' : 'VIN Not Verified'}
                          </button>
                        </div>
                        
                        <div>
                          <label className={mobileLabelStyles}>Color</label>
                          <input
                            type="text"
                            name="color"
                            value={editVehicleData.color || ''}
                            onChange={handleEditInputChange}
                            list="vehicle-colors-edit"
                            className={mobileInputStyles}
                          />
                          <datalist id="vehicle-colors-edit">
                            {VEHICLE_COLORS.map(color => (
                              <option key={color} value={color} />
                            ))}
                          </datalist>
                        </div>
                        <div>
                          <label className={mobileLabelStyles}>Status</label>
                          <select
                            name="status"
                            value={editVehicleData.status || 'FOUND'}
                            onChange={handleEditInputChange}
                            className={mobileInputStyles}>
                            <option value="FOUND">FOUND</option>
                            <option value="SECURED">SECURED</option>
                            <option value="NOT FOUND">NOT FOUND</option>
                            <option value="PENDING PICKUP">PENDING PICKUP</option>
                            <option value="DO NOT SECURE">DO NOT SECURE</option>
                          </select>
                        </div>
                        <div>
                          <label className={mobileLabelStyles}>Plate #</label>
                          <input
                            type="text"
                            name="plateNumber"
                            value={editVehicleData.plateNumber || ''}
                            onChange={handleEditInputChange}
                            className={mobileInputStyles}
                          />
                        </div>
                        <div>
                          <label className={mobileLabelStyles}>Account #</label>
                          <input
                            type="text"
                            name="accountNumber"
                            value={editVehicleData.accountNumber || ''}
                            onChange={handleEditInputChange}
                            className={mobileInputStyles}
                          />
                        </div>
                        <div>
                          <label className={mobileLabelStyles}>Secured Date</label>
                          <input
                            type="date"
                            name="securedDate"
                            value={editVehicleData.securedDate || ''}
                            onChange={handleEditInputChange}
                            className={mobileInputStyles}
                          />
                        </div>
                        <div className={isMobile ? '' : 'col-span-2'}>
                          <label className={mobileLabelStyles}>Financier</label>
                          <input
                            type="text"
                            name="financier"
                            value={editVehicleData.financier || ''}
                            onChange={handleEditInputChange}
                            className={mobileInputStyles}
                          />
                        </div>
                      </div>
                      
                      {/* Edit form address fields */}
                      <div className="mb-3">
                        <label className={mobileLabelStyles}>Street Address</label>
                        <input
                          type="text"
                          name="address"
                          value={editVehicleData.address || ''}
                          onChange={handleEditInputChange}
                          className={mobileInputStyles}
                        />
                      </div>

                      <div className={`grid ${isMobile ? 'grid-cols-1 gap-3' : 'grid-cols-3 gap-3'} mb-3`}>
                        <div>
                          <label className={mobileLabelStyles}>City</label>
                          <input
                            type="text"
                            name="city"
                            value={editVehicleData.city || ''}
                            onChange={handleEditInputChange}
                            className={mobileInputStyles}
                          />
                        </div>
                        <div>
                          <label className={mobileLabelStyles}>State</label>
                          <select
                            name="state"
                            value={editVehicleData.state || ''}
                            onChange={handleEditInputChange}
                            className={mobileInputStyles}
                          >
                            <option value="">Select State</option>
                            {STATE_OPTIONS.map(state => (
                              <option key={state.value} value={state.value}>
                                {state.label} ({state.value})
                              </option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label className={mobileLabelStyles}>ZIP Code</label>
                          <input
                            type="text"
                            name="zipCode"
                            value={editVehicleData.zipCode || ''}
                            onChange={handleEditInputChange}
                            maxLength={10}
                            className={mobileInputStyles}
                          />
                        </div>
                      </div>

                      {/* Edit form address preview */}
                      {(editVehicleData.address || editVehicleData.city || editVehicleData.state) && (
                        <div className="mb-3 bg-blue-900 bg-opacity-30 rounded-lg p-3 border border-blue-600">
                          <p className="text-blue-300 text-xs font-semibold mb-1">📍 Full Address:</p>
                          <p className="text-white font-semibold text-sm">
                            {buildFullAddress(editVehicleData.address, editVehicleData.city, editVehicleData.state, editVehicleData.zipCode)}
                          </p>
                        </div>
                      )}
                      
                      <div className="mb-3">
                        <label className={mobileLabelStyles}>Notes</label>
                        <textarea
                          name="notes"
                          value={editVehicleData.notes || ''}
                          onChange={handleEditInputChange}
                          rows="3"
                          className={mobileInputStyles}
                        />
                      </div>

                      {/* Image upload section for edit form - Mobile Enhanced */}
                      <div className="mb-3">
                        <label className={mobileLabelStyles}>Photos</label>
                        
                        {/* Hidden file inputs */}
                        <input
                          ref={editFileInputRef}
                          type="file"
                          accept="image/*"
                          multiple
                          onChange={handleEditFileSelect}
                          className="hidden"
                        />
                        
                        <input
                          ref={editCameraInputRef}
                          type="file"
                          accept="image/*"
                          capture="environment"
                          onChange={handleEditFileSelect}
                          className="hidden"
                        />
                        
                        {/* Mobile-friendly button group */}
                        <div className="flex gap-2 mb-2">
                          <button
                            onClick={() => editCameraInputRef.current?.click()}
                            disabled={editUploadingImages}
                            className={`flex-1 ${mobileInputStyles} hover:bg-gray-800 transition-colors flex items-center justify-center`}
                          >
                            {editUploadingImages ? `Uploading... ${Math.round(editImageUploadProgress)}%` : '📷 Camera'}
                          </button>
                          
                          <button
                            onClick={() => editFileInputRef.current?.click()}
                            disabled={editUploadingImages}
                            className={`flex-1 ${mobileInputStyles} hover:bg-gray-800 transition-colors flex items-center justify-center`}
                          >
                            📁 Gallery
                          </button>
                        </div>
                        
                        {editVehicleData.images && editVehicleData.images.length > 0 && (
                          <div className="text-xs text-gray-400">
                            {editVehicleData.images.length} photo(s) attached
                          </div>
                        )}
                      </div>

                      {/* Image Preview for Edit Form */}
                      {editVehicleData.images && editVehicleData.images.length > 0 && (
                        <div className="mb-4">
                          <p className={mobileLabelStyles}>Current Photos</p>
                          <div className={`grid ${isMobile ? 'grid-cols-3' : 'grid-cols-4'} gap-2`}>
                            {editVehicleData.images.map((image, idx) => (
                              <div key={idx} className="relative aspect-square rounded-lg overflow-hidden border border-gray-600">
                                <img 
                                  src={image.url} 
                                  alt={`Vehicle ${idx + 1}`}
                                  className="w-full h-full object-cover cursor-pointer"
                                  onClick={() => showImageInModal(image.url)}
                                />
                                <button
                                  onClick={() => removeImageFromEditVehicle(idx)}
                                  className="absolute top-1 right-1 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-700"
                                >
                                  ✕
                                </button>
                                {image.location && (
                                  <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1">
                                    📍 GPS
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {/* Mobile-optimized action buttons */}
                      <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'space-x-2'}`}>
                        <button
                          onClick={handleSaveVehicle}
                          className={`${isMobile ? 'w-full' : 'flex-1'} bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white ${isMobile ? 'py-3' : 'py-2'} rounded-lg font-semibold shadow-lg`}
                        >
                          Save Changes
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className={`${isMobile ? 'w-full' : 'flex-1'} bg-gray-600 hover:bg-gray-700 text-white ${isMobile ? 'py-3' : 'py-2'} rounded-lg font-semibold shadow-lg`}
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                );
              }

              // Regular mobile card view - Enhanced with all info visible including color and drive type
              return (
                <div 
                  key={vehicle.id} 
                  className={`bg-gray-800 bg-opacity-90 rounded-xl shadow-xl overflow-hidden border ${
                    isTeamSecured ? 'border-purple-500' : 
                    vehicle.bottomStatus ? 'border-red-500' :
                    vehicle.status === 'SECURED' ? 'border-green-500' :
                    vehicle.status === 'FOUND' ? 'border-yellow-500' :
                    vehicle.status === 'PENDING PICKUP' ? 'border-blue-500' :
                    vehicle.status === 'DO NOT SECURE' ? 'border-purple-500' :
                    'border-gray-700'
                  } backdrop-blur-lg`}
                >
                  {/* Status Bar */}
                  <div className={`px-4 py-2 ${
                    vehicle.status === 'SECURED' ? 'bg-green-600' :
                    vehicle.status === 'FOUND' ? 'bg-yellow-600' :
                    vehicle.status === 'PENDING PICKUP' ? 'bg-blue-600' :
                    vehicle.status === 'DO NOT SECURE' ? 'bg-purple-600' :
                    'bg-red-600'
                  }`}>
                    <div className="flex justify-between items-center">
                      <span className="font-bold text-white flex items-center">
                        {getStatusEmoji(vehicle.status)} {vehicle.status}
                      </span>
                      <div className="flex items-center space-x-2">
                        {vehicle.position && (
                          <span className="bg-green-700 text-green-100 text-xs px-2 py-1 rounded-full font-semibold">
                            📍 GPS
                          </span>
                        )}
                        {vehicle.bottomStatus && (
                          <span className="bg-red-700 text-red-100 text-xs px-2 py-1 rounded-full font-semibold">
                            {vehicle.bottomStatus}
                          </span>
                        )}
                        {vehicle.vinVerified && (
                          <span className="bg-green-700 text-green-100 text-xs px-2 py-1 rounded-full font-semibold">
                            ✓ VIN
                          </span>
                        )}
                        {vehicle.carriedOver && (
                          <span className="bg-orange-700 text-orange-100 text-xs px-2 py-1 rounded-full font-semibold">
                            C/O
                          </span>
                        )}
                        {isTeamSecured && (
                          <span className="bg-purple-700 text-purple-100 text-xs px-2 py-1 rounded-full font-semibold">
                            TEAM
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Vehicle Content */}
                  <div className="p-4">
                    {/* Vehicle Header */}
                    <div className="mb-3">
                      <h3 className="font-bold text-xl text-white mb-1">{vehicle.vehicle}</h3>
                      <div className="flex flex-wrap items-center gap-2">
                        <span className="inline-block bg-red-900 text-red-200 px-3 py-1 rounded-lg text-sm font-bold">
                          VIN: {vehicle.vin || 'N/A'}
                        </span>
                        {vehicle.color && (
                          <span className="inline-block bg-pink-900 text-pink-200 px-3 py-1 rounded-lg text-sm font-bold">
                            {vehicle.color}
                          </span>
                        )}
                        {vehicle.driveType && (
                          <span className="inline-block bg-purple-900 text-purple-200 px-3 py-1 rounded-lg text-sm font-bold">
                            {vehicle.driveType}
                          </span>
                        )}
                        <span className="text-gray-400 text-sm">
                          {vehicle.date}
                        </span>
                      </div>
                    </div>
                    
                    {/* Team Secured Info */}
                    {isTeamSecured && (
                      <div className="bg-purple-900 bg-opacity-30 rounded-lg p-3 mb-3 border border-purple-600">
                        <p className="text-purple-300 text-sm font-semibold">
                          🤝 Secured by teammate: {vehicle.securedByUserName}
                        </p>
                      </div>
                    )}
                    
                    {/* DO NOT SECURE Reason */}
                    {vehicle.status === 'DO NOT SECURE' && vehicle.doNotSecureReason && (
                      <div className="bg-purple-900 bg-opacity-30 rounded-lg p-3 mb-3 border border-purple-600">
                        <p className="text-purple-300 text-xs font-semibold mb-1">⚠️ DO NOT SECURE</p>
                        <p className="text-purple-100 font-bold">{vehicle.doNotSecureReason}</p>
                      </div>
                    )}
                    
                    {/* Bottom Status Info if present */}
                    {vehicle.bottomStatus && (
                      <div className="bg-red-900 bg-opacity-30 rounded-lg p-3 mb-3 border border-red-600">
                        <p className="text-red-300 text-xs font-semibold mb-1">⚠️ BOTTOM STATUS</p>
                        <p className="text-red-100 font-bold">{vehicle.bottomStatus}</p>
                        {vehicle.bottomStatusCount && (
                          <p className="text-red-200 text-xs mt-1">Attempt {vehicle.bottomStatusCount} of 3</p>
                        )}
                        {vehicle.bottomStatusByUserName && (
                          <p className="text-red-200 text-xs">By: {vehicle.bottomStatusByUserName}</p>
                        )}
                        {vehicle.bottomStatusDate && (
                          <p className="text-red-200 text-xs">Date: {new Date(vehicle.bottomStatusDate).toLocaleDateString()}</p>
                        )}
                      </div>
                    )}
                    
                    {/* Key Information Grid - Mobile Optimized */}
                    <div className={`grid ${isMobile ? 'grid-cols-2' : 'grid-cols-2'} gap-2 mb-3`}>
                      {vehicle.plateNumber && (
                        <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                          <p className="text-gray-400 text-xs font-semibold mb-1">Plate #</p>
                          <p className="text-orange-400 font-bold text-sm">{vehicle.plateNumber}</p>
                        </div>
                      )}
                      {vehicle.accountNumber && (
                        <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                          <p className="text-gray-400 text-xs font-semibold mb-1">Account #</p>
                          <p className="text-blue-400 font-bold text-sm">{vehicle.accountNumber}</p>
                        </div>
                      )}
                      {vehicle.financier && (
                        <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                          <p className="text-gray-400 text-xs font-semibold mb-1">Financier</p>
                          <p className="text-yellow-400 font-bold text-sm">{vehicle.financier}</p>
                        </div>
                      )}
                      {vehicle.securedDate && (
                        <div className="bg-gray-700 bg-opacity-50 rounded-lg p-2">
                          <p className="text-gray-400 text-xs font-semibold mb-1">Secured</p>
                          <p className="text-green-400 font-bold text-sm">{vehicle.securedDate}</p>
                        </div>
                      )}
                    </div>
                    
                    {/* Address - BIG for tow drivers */}
                    {(vehicle.address || vehicle.fullAddress) && (
                      <div 
                        className="bg-blue-900 bg-opacity-40 rounded-lg p-4 mb-3 border-2 border-blue-500 cursor-pointer hover:bg-blue-800 hover:bg-opacity-50 transition-all"
                        onClick={() => navigateToAddress(vehicle.fullAddress || buildFullAddress(vehicle.address, vehicle.city, vehicle.state, vehicle.zipCode))}
                      >
                        <p className="text-blue-300 text-sm font-bold mb-2 flex items-center">
                          <span className="mr-2 text-lg">📍</span>
                          VEHICLE LOCATION - TAP TO NAVIGATE
                        </p>
                        <p className="text-white font-bold text-lg">
                          {vehicle.fullAddress || buildFullAddress(vehicle.address, vehicle.city, vehicle.state, vehicle.zipCode)}
                        </p>
                        {vehicle.position && (
                          <p className="text-blue-200 text-sm mt-2 font-mono">
                            GPS: {vehicle.position.lat.toFixed(6)}, {vehicle.position.lng.toFixed(6)}
                          </p>
                        )}
                      </div>
                    )}
                    
                    {/* Images Section - Bigger for mobile */}
                    {vehicle.images && vehicle.images.length > 0 && (
                      <div className="mb-3">
                        <p className="text-gray-400 text-sm font-semibold mb-2 flex items-center">
                          <span className="mr-1">📸</span>
                          PHOTOS ({vehicle.images.length})
                        </p>
                        <div className={`grid ${isMobile ? 'grid-cols-2' : 'grid-cols-4'} gap-2`}>
                          {vehicle.images.slice(0, isMobile ? 4 : 4).map((image, idx) => (
                            <div 
                              key={idx}
                              className="aspect-square rounded-lg overflow-hidden cursor-pointer border-2 border-gray-600 hover:border-blue-500 transition-all"
                              onClick={() => showImageInModal(image.url)}
                            >
                              <img 
                                src={image.url} 
                                alt={`Vehicle ${idx + 1}`}
                                className="w-full h-full object-cover"
                              />
                            </div>
                          ))}
                          {vehicle.images.length > 4 && (
                            <div 
                              className="aspect-square rounded-lg bg-gray-700 flex items-center justify-center cursor-pointer border-2 border-gray-600 hover:border-blue-500 transition-all"
                              onClick={() => showImageInModal(vehicle.images[4].url)}
                            >
                              <span className="text-gray-300 text-sm font-semibold">
                                +{vehicle.images.length - 4} more
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {/* Notes Section */}
                    <div className="bg-gray-700 bg-opacity-50 rounded-lg p-3 mb-3">
                      <div className="flex justify-between items-center mb-1">
                        <p className="text-gray-400 text-sm font-semibold flex items-center">
                          <span className="mr-1">📝</span>
                          NOTES
                        </p>
                        {editingNotes !== vehicle.id && (
                          <button
                            onClick={() => handleEditNotes(vehicle.id, vehicle.notes)}
                            className="text-blue-400 hover:text-blue-300 text-sm"
                          >
                            Edit
                          </button>
                        )}
                      </div>
                      {editingNotes === vehicle.id ? (
                        <div>
                          <textarea
                            value={tempNotes}
                            onChange={(e) => setTempNotes(e.target.value)}
                            className={`w-full ${isMobile ? 'text-base p-3' : 'text-sm p-2'} bg-gray-800 border border-gray-600 rounded mb-2`}
                            rows="3"
                            placeholder="Add notes..."
                          />
                          <div className="flex space-x-2">
                            <button
                              onClick={handleSaveNotes}
                              className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-semibold"
                            >
                              Save
                            </button>
                            <button
                              onClick={handleCancelNotes}
                              className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm font-semibold"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      ) : (
                        <p className="text-gray-200 text-sm">
                          {vehicle.notes || <span className="text-gray-500 italic">No notes added</span>}
                        </p>
                      )}
                    </div>
                    
                    {/* Action Buttons - Mobile Optimized */}
                    <div className={`grid ${isMobile ? 'grid-cols-2' : 'grid-cols-3'} gap-2`}>
                      <button
                        onClick={() => handleEditVehicle(vehicle)}
                        className={`bg-blue-600 hover:bg-blue-700 text-white ${isMobile ? 'py-3' : 'py-2'} rounded-lg font-semibold text-sm shadow-lg transition-all`}
                      >
                        ✏️ Edit
                      </button>
                      {!isMobile && (
                        <button
                          onClick={() => handleDeleteVehicle(vehicle.id)}
                          className="bg-red-600 hover:bg-red-700 text-white py-2 rounded-lg font-semibold text-sm shadow-lg transition-all"
                        >
                          🗑️ Delete
                        </button>
                      )}
                      <select
                        value={vehicle.status}
                        onChange={(e) => handleStatusChange(vehicle.id, e.target.value)}
                        className={`bg-gray-700 text-white border border-gray-600 rounded-lg px-2 ${isMobile ? 'py-3' : 'py-2'} text-sm font-semibold`}
                        disabled={vehicle.securedByTeammate}
                        title={vehicle.securedByTeammate ? 'Secured by teammate - cannot change' : 'Change status'}
                      >
                        <option value="FOUND">Found</option>
                        <option value="SECURED">Secured</option>
                        <option value="NOT FOUND">Not Found</option>
                        <option value="PENDING PICKUP">Pending</option>
                        <option value="DO NOT SECURE">Do Not Secure</option>
                      </select>
                      {isMobile && (
                        <button
                          onClick={() => handleDeleteVehicle(vehicle.id)}
                          className="bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-semibold text-sm shadow-lg transition-all col-span-2"
                        >
                          🗑️ Delete Vehicle
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Team Sync Legend - Enhanced */}
        <div className="mt-4 p-4 bg-gray-800 bg-opacity-90 rounded-xl border border-gray-700 shadow-xl">
          <h4 className="text-sm font-bold text-gray-200 mb-3 flex items-center">
            <span className="mr-2">ℹ️</span>
            Legend & Information
          </h4>
          <div className={`grid ${isMobile ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-6'} gap-3 text-xs`}>
            <div className="flex items-center bg-gray-700 bg-opacity-50 rounded-lg p-2">
              <span className="inline-block w-4 h-4 bg-green-600 rounded mr-2"></span>
              <span className="text-gray-300">Secured</span>
            </div>
            <div className="flex items-center bg-gray-700 bg-opacity-50 rounded-lg p-2">
              <span className="inline-block w-4 h-4 bg-yellow-600 rounded mr-2"></span>
              <span className="text-gray-300">Found</span>
            </div>
            <div className="flex items-center bg-gray-700 bg-opacity-50 rounded-lg p-2">
              <span className="inline-block w-4 h-4 bg-blue-600 rounded mr-2"></span>
              <span className="text-gray-300">Pending</span>
            </div>
            <div className="flex items-center bg-gray-700 bg-opacity-50 rounded-lg p-2">
              <span className="inline-block w-4 h-4 bg-purple-600 rounded mr-2"></span>
              <span className="text-gray-300">Do Not Secure/Team</span>
            </div>
            <div className="flex items-center bg-gray-700 bg-opacity-50 rounded-lg p-2">
              <span className="inline-block w-4 h-4 bg-red-600 rounded mr-2"></span>
              <span className="text-gray-300">Bottom</span>
            </div>
            <div className="flex items-center bg-gray-700 bg-opacity-50 rounded-lg p-2">
              <span className="inline-block w-4 h-4 bg-orange-600 rounded mr-2"></span>
              <span className="text-gray-300">Carryover</span>
            </div>
          </div>

          {/* Mobile Tips */}
          {isMobile && (
            <div className="mt-3 p-3 bg-green-900 bg-opacity-30 border border-green-600 rounded-lg">
              <div className="text-sm text-green-200">
                <strong className="flex items-center mb-1">
                  <span className="mr-2">📱</span>
                  Mobile Tips
                </strong>
                <ul className="text-xs space-y-1">
                  <li>• Tap address to open navigation</li>
                  <li>• Tap photos to view full size</li>
                  <li>• Use camera button for quick photos</li>
                  <li>• GPS location auto-fills address</li>
                </ul>
              </div>
            </div>
          )}

          {/* GPS Status Info */}
          <div className="mt-3 p-3 bg-green-900 bg-opacity-30 border border-green-600 rounded-lg">
            <div className="text-sm text-green-200">
              <strong className="flex items-center mb-1">
                <span className="mr-2">📍</span>
                GPS Location Feature
              </strong>
              <p className="text-xs">
                Click "Get Current Location & Address" to automatically capture GPS coordinates and street address when adding vehicles.
              </p>
            </div>
          </div>

          {/* Auto Drive Type Info */}
          <div className="mt-3 p-3 bg-purple-900 bg-opacity-30 border border-purple-600 rounded-lg">
            <div className="text-sm text-purple-200">
              <strong className="flex items-center mb-1">
                <span className="mr-2">🚗</span>
                Auto Drive Type Detection
              </strong>
              <p className="text-xs">
                Drive type (FWD, RWD, AWD, 4WD) is automatically detected from the vehicle make/model. The system shows possible sub-models and their drive types when you enter a vehicle name.
              </p>
            </div>
          </div>

          {/* Status Info */}
          <div className="mt-3 p-3 bg-purple-900 bg-opacity-30 border border-purple-600 rounded-lg">
            <div className="text-sm text-purple-200">
              <strong className="flex items-center mb-1">
                <span className="mr-2">🚫</span>
                Status Information
              </strong>
              <p className="text-xs">
                All orders default to "PENDING PICKUP" status. Use the "DO NOT SECURE" button only for special cases:
              </p>
              <ul className="text-xs mt-1 ml-4">
                <li>• DO NOT USE LIST</li>
                <li>• BLOCKED IN</li>
                <li>• PEOPLE IN VEHICLE</li>
                <li>• OTHER (custom reason)</li>
              </ul>
            </div>
          </div>

          {/* Bottom Status Types */}
          <div className="mt-3 p-3 bg-red-900 bg-opacity-30 border border-red-600 rounded-lg">
            <div className="text-sm text-red-200">
              <strong className="flex items-center mb-1">
                <span className="mr-2">⚠️</span>
                Bottom Status Types
              </strong>
              <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-2'} gap-2 text-xs mt-2`}>
                <div>• GONE - Vehicle not at location</div>
                <div>• BLOCKED IN - Cannot access</div>
                <div>• CANT SECURE - Unable to tow</div>
                <div>• DEBTOR INTERFERENCE - Owner issue</div>
              </div>
              <p className="text-xs mt-2">After 3 attempts, vehicles are moved to Never Secured list</p>
            </div>
          </div>

          {/* Carryover Info */}
          <div className="mt-3 p-3 bg-orange-900 bg-opacity-30 border border-orange-600 rounded-lg">
            <div className="text-sm text-orange-200">
              <strong className="flex items-center mb-1">
                <span className="mr-2">🔄</span>
                Carryover Vehicles
              </strong>
              <p className="text-xs">
                Vehicles not secured from previous weeks automatically carry over to the current week. Use "Check Carryovers" button to manually sync missing vehicles. Duplicate VINs are automatically prevented.
              </p>
            </div>
          </div>

          {/* NEW: Orders Integration Info */}
          <div className="mt-3 p-3 bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg">
            <div className="text-sm text-blue-200">
              <strong className="flex items-center mb-1">
                <span className="mr-2">📋</span>
                Orders Integration
              </strong>
              <p className="text-xs">
                Open orders from your team are now displayed on the map as blue folder icons. Click any order to view details, navigate to location, or mark as secured. Only open orders are shown to reduce map clutter.
              </p>
            </div>
          </div>

          {team && (
            <div className="mt-3 p-3 bg-purple-900 bg-opacity-30 border border-purple-600 rounded-lg">
              <div className="text-sm text-purple-200">
                <strong className="flex items-center mb-1">
                  <span className="mr-2">🔄</span>
                  Team Sync Active
                </strong>
                <p className="text-xs">
                  When you secure a vehicle, it syncs across all team members in <strong>{team.name}</strong>. Orders are also synced across the team.
                </p>
              </div>
            </div>
          )}

          {!canSecureVehicles && (
            <div className="mt-3 p-3 bg-yellow-900 bg-opacity-30 border border-yellow-600 rounded-lg">
              <div className="text-sm text-yellow-200">
                <strong className="flex items-center mb-1">
                  <span className="mr-2">⚠️</span>
                  Limited Access
                </strong>
                <p className="text-xs">
                  You can add vehicles with "PENDING PICKUP" status only. Tow truck drivers and admins can secure vehicles and orders.
                </p>
              </div>
            </div>
          )}

          <div className="mt-3 flex justify-between items-center">
            <SoundToggle />
            <button
              onClick={() => window.location.reload()}
              className="text-blue-400 hover:text-blue-300 text-sm font-semibold flex items-center"
            >
              <span className="mr-1">🔄</span>
              Refresh App
            </button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-black bg-opacity-50 backdrop-blur-lg border-t border-gray-700 mt-8 -mx-4 px-4 py-4">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between text-sm text-gray-300">
            <div className="flex flex-wrap items-center gap-2 mb-2 md:mb-0">
              <span className="font-semibold">NWRepo Vehicle Tracker</span>
              <span>•</span>
              <span className="text-blue-400">Mobile Enhanced v3.2</span>
              {team && (
                <>
                  <span>•</span>
                  <span className="text-purple-400">Team: {team.name}</span>
                </>
              )}
              {canSecureVehicles && (
                <>
                  <span>•</span>
                  <span className="text-green-400">🚚 Tow Truck Access</span>
                </>
              )}
              {/* NEW: Orders integration indicator */}
              {orders.length > 0 && (
                <>
                  <span>•</span>
                  <span className="text-cyan-400">📋 Orders Integrated</span>
                </>
              )}
            </div>
            <div className="flex flex-wrap items-center gap-3">
              <span className="bg-gray-800 px-2 py-1 rounded">
                Weekly: <span className="font-bold text-green-400">{weeklyStats.weeklySecured}</span> secured
              </span>
              <span className="bg-gray-800 px-2 py-1 rounded">
                YTD: <span className="font-bold text-blue-400">{ytdStats.totalSecured}</span> secured
              </span>
              {/* NEW: Orders count in footer */}
              {orders.length > 0 && (
                <span className="bg-gray-800 px-2 py-1 rounded">
                  Orders: <span className="font-bold text-cyan-400">{orders.length}</span> open
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  {/* Image Modal - Mobile Optimized */}
  {showImageModal && selectedImage && (
    <div 
      className="fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center p-2"
      onClick={() => {
        setShowImageModal(false);
        setSelectedImage(null);
      }}
    >
      <div className="relative max-w-full max-h-full">
        <img 
          src={selectedImage} 
          alt="Vehicle"
          className="max-w-full max-h-[90vh] rounded-lg shadow-2xl"
          style={{ maxHeight: '90vh', width: 'auto', height: 'auto' }}
          onClick={(e) => e.stopPropagation()}
        />
        <button
          onClick={() => {
            setShowImageModal(false);
            setSelectedImage(null);
          }}
          className={`absolute top-2 right-2 bg-red-600 text-white rounded-full ${isMobile ? 'w-12 h-12' : 'w-10 h-10'} flex items-center justify-center hover:bg-red-700 shadow-lg text-xl`}
        >
          ✕
        </button>
      </div>
    </div>
  )}

  {/* Order Card Modal */}
  {showOrderCard && selectedOrderForCard && (
    <OrderCardModal
      order={selectedOrderForCard}
      onClose={() => {
        setShowOrderCard(false);
        setSelectedOrderForCard(null);
      }}
      onNavigate={handleOrderNavigate}
      onSecure={handleOrderSecure}
      currentUser={user}
      userProfile={userProfile}
      db={db}
      team={team}
      isMobile={isMobile}
    />
  )}

  {/* Print View (visible only when printing) */}
  {showPrintView && <PrintView />}

  {/* Print-specific styles - only applied when printing */}
  <style type="text/css" media="print">
    {`
    @page { size: landscape; margin: 0.5in; }

    /* Hide regular UI during print */
    body > *:not(.print-container) {
      display: none !important;
    }

    /* Show print container */
    .print-container {
      display: block !important;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }

    /* Print-specific styling */
    .print-container {
      background-color: white !important;
      color: black !important;
    }

    .print-container table {
      width: 100%;
      border-collapse: collapse;
    }

    .print-container th,
    .print-container td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }

    .print-container th {
      background-color: #f2f2f2;
      font-weight: bold;
    }

    .print-container tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    `}
  </style>
</>
); 
}

export default StandaloneVehicleTracker;